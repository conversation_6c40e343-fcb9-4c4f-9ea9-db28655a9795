import * as fs from 'fs';
import * as path from 'path';
import { OracleSqlNodeCredentials } from './interfaces';

export class OracleClientManager {
	private static instance: OracleClientManager;
	private instantClientPath: string;
	private tnsAdminPath: string;
	private isInitialized = false;

	private constructor() {
		// Caminho para o Oracle Instant Client bundled
		const projectRoot = process.cwd();

		// Sempre usar a pasta nodes (código fonte) onde está o instantclient
		const basePath = path.join(projectRoot, 'nodes', 'OracleSql', 'v2', 'lib');

		this.instantClientPath = path.join(basePath, 'instantclient');
		// TNS Admin será a própria pasta instantclient
		this.tnsAdminPath = this.instantClientPath;
	}

	public static getInstance(): OracleClientManager {
		if (!OracleClientManager.instance) {
			OracleClientManager.instance = new OracleClientManager();
		}
		return OracleClientManager.instance;
	}

	/**
	 * Inicializa o Oracle Client com o Instant Client bundled
	 */
	public async initializeOracleClient(): Promise<boolean> {
		try {
			const oracledb = require('oracledb');

			console.log('─'.repeat(60));
			console.log(`🔍 Verificando Oracle Instant Client bundled em: ${this.instantClientPath}`);
			console.log(`🔍 TNS Admin será configurado em: ${this.tnsAdminPath}`);
			console.log(`🔍 Diretório atual: ${process.cwd()}`);
			console.log(`🔍 __dirname: ${__dirname}`);

			// Verificar se já está no modo thick
			if (oracledb.thin === false) {
				console.log('✅ Oracle Client já está no modo thick');
				this.isInitialized = true;
				console.log('─'.repeat(60));
				return true;
			}

			// Se já tentamos inicializar e falhou, não tentar novamente
			if (this.isInitialized) {
				console.log('⚠️ Oracle Client já foi inicializado, mas não está no modo thick');
				console.log('─'.repeat(60));
				return false;
			}

			// Verificar se o Instant Client bundled existe
			if (!fs.existsSync(this.instantClientPath)) {
				console.warn(
					`⚠️ Oracle Instant Client bundled não encontrado em: ${this.instantClientPath}`,
				);

				// Tentar caminhos alternativos
				const alternativePaths = [
					path.join(process.cwd(), 'nodes', 'OracleSql', 'v2', 'lib', 'instantclient'),
					path.join(__dirname, '..', '..', 'lib', 'instantclient'),
					path.join(__dirname, '..', 'lib', 'instantclient'),
				];

				console.log('🔍 Tentando caminhos alternativos:');
				for (const altPath of alternativePaths) {
					console.log(`   - ${altPath}: ${fs.existsSync(altPath) ? '✅ EXISTE' : '❌ NÃO EXISTE'}`);
					if (fs.existsSync(altPath)) {
						this.instantClientPath = altPath;
						this.tnsAdminPath = altPath; // TNS Admin é a própria pasta instantclient
						console.log(`✅ Usando caminho alternativo: ${this.instantClientPath}`);
						break;
					}
				}

				// Verificar novamente após tentar caminhos alternativos
				if (!fs.existsSync(this.instantClientPath)) {
					console.warn('📦 Para usar CQN, extraia o Oracle Instant Client nesta pasta');
					console.warn('💡 Execute: npm run install-instantclient');
					console.log('─'.repeat(60));
					return false;
				}
			}

			// Verificar se oci.dll existe e sua arquitetura
			const ociPath = path.join(this.instantClientPath, 'oci.dll');
			if (!fs.existsSync(ociPath)) {
				console.warn(`⚠️ oci.dll não encontrado em: ${ociPath}`);
				console.warn('📦 Oracle Instant Client pode estar incompleto');
				console.log('─'.repeat(60));
				return false;
			}

			console.log('✅ Oracle Instant Client bundled detectado');
			console.log(
				`🔄 Inicializando Oracle Client com Instant Client bundled: ${this.instantClientPath}`,
			);

			// Limpar PATH do sistema para evitar conflito com Oracle x32
			this.clearSystemOracleEnvVars();

			// Configurar variáveis de ambiente para usar apenas o bundled
			process.env.TNS_ADMIN = this.tnsAdminPath;
			process.env.ORACLE_HOME = this.instantClientPath;
			process.env.OCI_LIB_DIR = this.instantClientPath;
			process.env.LD_LIBRARY_PATH = this.instantClientPath;

			// Inicializar Oracle Client
			console.log('🚀 Inicializando Oracle Client no modo thick...');
			oracledb.initOracleClient({
				libDir: this.instantClientPath,
				configDir: this.tnsAdminPath,
			});

			// Aguardar um momento para a inicialização
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Verificar se a inicialização foi bem-sucedida
			if (oracledb.thin === false) {
				this.isInitialized = true;
				console.log('✅ Oracle Client inicializado com sucesso no modo thick');
				console.log(`📁 TNS_ADMIN configurado para: ${this.tnsAdminPath}`);
				console.log(`🔗 Oracle Client Version: ${oracledb.versionString || 'N/A'}`);
				console.log(`🏗️ Usando Oracle Instant Client bundled (x64)`);

				// Forçar o modo thick globalmente
				process.env.NODE_ORACLEDB_DRIVER_MODE = 'thick';

				console.log('─'.repeat(60));
				return true;
			} else {
				console.warn('⚠️ Oracle Client não conseguiu inicializar no modo thick');
				this.isInitialized = true; // Marcar como tentado
				console.log('─'.repeat(60));
				return false;
			}
		} catch (error) {
			console.error('❌ Erro ao inicializar Oracle Client:', error.message);
			console.warn('⚠️ Continuando sem modo thick. CQN pode não funcionar.');

			// Se o erro for de arquitetura, dar dica específica
			if (error.message.includes('DPI-1047')) {
				console.error('💡 Erro de arquitetura detectado!');
				console.error('🔧 Solução: O sistema está tentando usar Oracle Instant Client x32');
				console.error('✅ Este projeto usa Oracle Instant Client x64 bundled');
				console.error('🚀 Reinicie o n8n para usar o cliente bundled');
			}

			console.log('─'.repeat(60));
			return false;
		}
	}

	/**
	 * Limpa variáveis de ambiente que podem apontar para Oracle do sistema
	 */
	private clearSystemOracleEnvVars(): void {
		console.log('🧹 Removendo Oracle x32 do PATH para evitar conflito...');

		// Remover caminhos do Oracle x32 do PATH
		if (process.env.PATH) {
			const pathEntries = process.env.PATH.split(';');
			const filteredPath = pathEntries.filter((entry) => {
				const lowerEntry = entry.toLowerCase();
				// Remove qualquer caminho que contenha 'instantclient' mas não seja o nosso bundled
				return (
					!lowerEntry.includes('instantclient') ||
					lowerEntry.includes(this.instantClientPath.toLowerCase())
				);
			});

			if (filteredPath.length !== pathEntries.length) {
				process.env.PATH = filteredPath.join(';');
				console.log('✅ Oracle x32 removido do PATH');
			}
		}
	}

	/**
	 * Cria um arquivo tnsnames.ora dinâmico baseado nas credenciais na pasta instantclient
	 */
	public createTnsNames(
		credentials: OracleSqlNodeCredentials,
		serviceName = 'ORACLE_SERVICE',
	): string {
		try {
			console.log('📝 Criando tnsnames.ora na pasta instantclient...');

			// Extrair host e porta da connectString
			let host = 'localhost';
			let port = '1521';
			let service = 'XE';

			// Parse da connectString
			if (credentials.connectString) {
				console.log(`🔍 Analisando connectString: ${credentials.connectString}`);

				// Formato: host:port/service ou host:port:sid
				if (credentials.connectString.includes('/')) {
					// Formato: host:port/service
					const [hostPort, serviceStr] = credentials.connectString.split('/');
					if (hostPort.includes(':')) {
						[host, port] = hostPort.split(':');
					} else {
						host = hostPort;
					}
					service = serviceStr;
				} else if (credentials.connectString.includes(':')) {
					// Formato: host:port ou host:port:sid
					const parts = credentials.connectString.split(':');
					host = parts[0];
					if (parts.length >= 2) {
						port = parts[1];
					}
					if (parts.length >= 3) {
						service = parts[2];
					}
				} else {
					// Apenas host
					host = credentials.connectString;
				}
			}

			// Conteúdo do tnsnames.ora
			const tnsContent = `${serviceName} =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = ${host})(PORT = ${port}))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ${service})
    )
  )

# Configuração adicional para CQN
${serviceName}_CQN =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = ${host})(PORT = ${port}))
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SERVICE_NAME = ${service})
    )
  )
`;

			const tnsFilePath = path.join(this.instantClientPath, 'tnsnames.ora');
			fs.writeFileSync(tnsFilePath, tnsContent, 'utf8');

			console.log(`📝 tnsnames.ora criado em: ${tnsFilePath}`);
			console.log(`🔗 Service Name: ${serviceName}`);
			console.log(`🏠 Host: ${host}:${port}`);
			console.log(`🎯 Service: ${service}`);

			return serviceName;
		} catch (error) {
			console.error('❌ Erro ao criar tnsnames.ora:', error.message);
			return credentials.connectString || 'localhost:1521/XE';
		}
	}

	/**
	 * Cria um arquivo sqlnet.ora para configurações adicionais na pasta instantclient
	 */
	public createSqlNetOra(): void {
		try {
			const sqlnetContent = `# Configurações para Oracle SQL*Net
NAMES.DIRECTORY_PATH = (TNSNAMES, EZCONNECT)
SQLNET.AUTHENTICATION_SERVICES = (NTS)
SQLNET.EXPIRE_TIME = 10

# Configurações para CQN (Continuous Query Notification)
SQLNET.INBOUND_CONNECT_TIMEOUT = 60
SQLNET.RECV_TIMEOUT = 30
SQLNET.SEND_TIMEOUT = 30

# Configurações de segurança
SQLNET.ENCRYPTION_CLIENT = ACCEPTED
SQLNET.ENCRYPTION_TYPES_CLIENT = (AES256, AES192, AES128)
SQLNET.CRYPTO_CHECKSUM_CLIENT = ACCEPTED
SQLNET.CRYPTO_CHECKSUM_TYPES_CLIENT = (SHA256, SHA1)
`;

			const sqlnetFilePath = path.join(this.instantClientPath, 'sqlnet.ora');
			fs.writeFileSync(sqlnetFilePath, sqlnetContent, 'utf8');

			console.log(`📝 sqlnet.ora criado em: ${sqlnetFilePath}`);
		} catch (error) {
			console.error('❌ Erro ao criar sqlnet.ora:', error.message);
		}
	}

	/**
	 * Verifica se o Oracle Instant Client bundled está disponível
	 */
	public isInstantClientAvailable(): boolean {
		const ociPath = path.join(this.instantClientPath, 'oci.dll');
		return fs.existsSync(ociPath);
	}

	/**
	 * Retorna o caminho do Oracle Instant Client bundled
	 */
	public getInstantClientPath(): string {
		return this.instantClientPath;
	}

	/**
	 * Retorna o caminho do TNS Admin
	 */
	public getTnsAdminPath(): string {
		return this.tnsAdminPath;
	}

	/**
	 * Cria uma connectString otimizada para CQN
	 */
	public createCqnConnectString(credentials: OracleSqlNodeCredentials): string {
		try {
			// Se o Oracle Instant Client está disponível e inicializado, usar TNS
			if (this.isInstantClientAvailable() && this.isInitialized) {
				const serviceName = this.createTnsNames(credentials, 'ORACLE_CQN');
				this.createSqlNetOra();
				console.log(`📝 TNS configurado. Usando service name: ${serviceName}`);
				return serviceName;
			}

			// Fallback: usar connectString original
			console.log(`⚠️ Oracle Instant Client não disponível. Usando connectString original.`);
			return credentials.connectString || 'localhost:1521/XE';
		} catch (error) {
			console.error('❌ Erro ao criar connectString para CQN:', error.message);
			console.log(`🔄 Fallback: usando connectString original`);
			return credentials.connectString || 'localhost:1521/XE';
		}
	}

	/**
	 * Limpa arquivos TNS temporários da pasta instantclient
	 */
	public cleanup(): void {
		try {
			const tnsFiles = ['tnsnames.ora', 'sqlnet.ora'];
			for (const file of tnsFiles) {
				const filePath = path.join(this.instantClientPath, file);
				if (fs.existsSync(filePath)) {
					fs.unlinkSync(filePath);
					console.log(`🧹 Removido: ${file}`);
				}
			}
			console.log('🧹 Arquivos TNS temporários removidos da pasta instantclient');
		} catch (error) {
			console.error('❌ Erro ao limpar arquivos TNS:', error.message);
		}
	}
}

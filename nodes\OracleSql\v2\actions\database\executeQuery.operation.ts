import type {
	IDataObject,
	IExecuteFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';
import { NodeOperationError } from 'n8n-workflow';

import type { QueryRunner, QueryWithValues } from '../../helpers/interfaces';

import { prepareQueryAndReplacements, replaceEmptyStringsByNulls } from '../../helpers/utils';

import { getResolvables, updateDisplayOptions } from '../../../../../utils/utilities';
import { optionsCollection } from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Query',
		name: 'query',
		type: 'string',
		default: '',
		placeholder: 'e.g. SELECT id, name FROM product WHERE id < 40',
		required: true,
		description:
			"The SQL query to execute. You can use n8n expressions and $1, $2, $3, etc to refer to the 'Query Parameters' set in options below.",
		noDataExpression: true,
		typeOptions: {
			editor: 'sqlEditor',
			sqlDialect: 'PLSQL',
		},
		hint: 'Consider using query parameters to prevent SQL injection attacks. Add them in the options below',
	},
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['executeQuery'],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
	nodeOptions: IDataObject,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);

	// Debug: Log pairedItem information
	console.log('🔍 ExecuteQuery - Input items pairedItem info:');
	items.forEach((item, index) => {
		console.log(`  Item ${index}: pairedItem =`, item.pairedItem);
	});

	const queries: QueryWithValues[] = [];

	for (let i = 0; i < items.length; i++) {
		let rawQuery = this.getNodeParameter('query', i) as string;

		// Preserva os comentários e formatação substituindo apenas as expressões n8n
		const expressions = getResolvables(rawQuery);
		for (const resolvable of expressions) {
			const evaluated = this.evaluateExpression(resolvable, i) as string;
			// Usa replace com regex para substituir mantendo a estrutura
			rawQuery = rawQuery.replace(
				new RegExp(resolvable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
				evaluated,
			);
		}

		const options = this.getNodeParameter('options', i, {});

		let values;
		let queryReplacement = options.queryReplacement || [];

		if (typeof queryReplacement === 'string') {
			queryReplacement = queryReplacement.split(',').map((entry) => entry.trim());
		}

		if (Array.isArray(queryReplacement)) {
			values = queryReplacement as IDataObject[];
		} else {
			throw new NodeOperationError(
				this.getNode(),
				'Query Replacement must be a string of comma-separated values, or an array of values',
				{ itemIndex: i },
			);
		}

		// Prepara a query preservando a formatação original para PL/SQL e queries com comentários
		// A função prepareQueryAndReplacements já melhora automaticamente os blocos PL/SQL para capturar ROWIDs
		const preparedQuery = prepareQueryAndReplacements(rawQuery, values);

		// Verifica se é um bloco PL/SQL ou tem comentários
		const isPLSQL =
			rawQuery.trim().toUpperCase().startsWith('DECLARE') ||
			rawQuery.trim().toUpperCase().startsWith('BEGIN');
		const hasComments = rawQuery.includes('--') || rawQuery.includes('/*');

		// Garante que a formatação seja preservada para blocos PL/SQL e queries com comentários
		if (isPLSQL || hasComments) {
			// Mantém a formatação, mas normaliza as quebras de linha
			preparedQuery.query = preparedQuery.query.replace(/\r\n/g, '\n');
		}

		if ((nodeOptions.nodeVersion as number) >= 2.3) {
			const parsedNumbers = preparedQuery.values.map((value) => {
				return Number(value) ? Number(value) : value;
			});
			preparedQuery.values = parsedNumbers;
		}

		queries.push(preparedQuery);
	}
	returnData = await runQueries(queries);

	// Debug: Log returnData pairedItem information
	console.log('🔍 ExecuteQuery - returnData from runQueries:');
	returnData.forEach((item, index) => {
		console.log(`  ReturnData ${index}: pairedItem =`, item.pairedItem);
	});

	let processedData: INodeExecutionData[] = [];

	// Se não há dados de retorno, retorna os items originais preservando pairedItem
	if (returnData.length === 0) {
		console.log('🔍 ExecuteQuery - No returnData, preserving original items');
		return items.map((item) => ({
			json: { not_found: true },
			pairedItem: item.pairedItem,
			binary: item.binary,
		}));
	}

	for (let i = 0; i < returnData.length; i++) {
		const queryResult = returnData[i];
		// Determina qual item original usar como base para pairedItem
		const sourceItem = items[Math.min(i, items.length - 1)];

		if (!queryResult || !queryResult.json || Object.keys(queryResult.json).length === 0) {
			processedData.push({
				json: {
					not_found: true,
					//query: queries[i].query, // Optional: include the query for reference
				},
				pairedItem: sourceItem?.pairedItem || { item: i },
				binary: sourceItem?.binary,
			});
		} else {
			// Preserva pairedItem e binary do resultado original ou do item fonte
			processedData.push({
				json: queryResult.json,
				pairedItem: queryResult.pairedItem || sourceItem?.pairedItem || { item: i },
				binary: queryResult.binary || sourceItem?.binary,
			});
		}
	}

	// Debug: Log final pairedItem information
	console.log('🔍 ExecuteQuery - Final processedData pairedItem info:');
	processedData.forEach((item, index) => {
		console.log(`  Result ${index}: pairedItem =`, item.pairedItem);
	});

	return processedData;
}

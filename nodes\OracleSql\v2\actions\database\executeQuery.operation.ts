import type {
	IDataObject,
	IExecuteFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';
import { NodeOperationError } from 'n8n-workflow';

import type { QueryRunner, QueryWithValues } from '../../helpers/interfaces';

import { prepareQueryAndReplacements, replaceEmptyStringsByNulls } from '../../helpers/utils';

import { getResolvables, updateDisplayOptions } from '../../../../../utils/utilities';
import { optionsCollection } from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Query',
		name: 'query',
		type: 'string',
		default: '',
		placeholder: 'e.g. SELECT id, name FROM product WHERE id < 40',
		required: true,
		description:
			"The SQL query to execute. You can use n8n expressions and $1, $2, $3, etc to refer to the 'Query Parameters' set in options below.",
		noDataExpression: true,
		typeOptions: {
			editor: 'sqlEditor',
			sqlDialect: 'PLSQL',
		},
		hint: 'Consider using query parameters to prevent SQL injection attacks. Add them in the options below',
	},
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['executeQuery'],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
	nodeOptions: IDataObject,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);

	const queries: QueryWithValues[] = [];

	for (let i = 0; i < items.length; i++) {
		let rawQuery = this.getNodeParameter('query', i) as string;

		// Preserva os comentários e formatação substituindo apenas as expressões n8n
		const expressions = getResolvables(rawQuery);
		for (const resolvable of expressions) {
			const evaluated = this.evaluateExpression(resolvable, i) as string;
			// Usa replace com regex para substituir mantendo a estrutura
			rawQuery = rawQuery.replace(
				new RegExp(resolvable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
				evaluated,
			);
		}

		const options = this.getNodeParameter('options', i, {});

		let values;
		let queryReplacement = options.queryReplacement || [];

		if (typeof queryReplacement === 'string') {
			queryReplacement = queryReplacement.split(',').map((entry) => entry.trim());
		}

		if (Array.isArray(queryReplacement)) {
			values = queryReplacement as IDataObject[];
		} else {
			throw new NodeOperationError(
				this.getNode(),
				'Query Replacement must be a string of comma-separated values, or an array of values',
				{ itemIndex: i },
			);
		}

		// Prepara a query preservando a formatação original para PL/SQL e queries com comentários
		// A função prepareQueryAndReplacements já melhora automaticamente os blocos PL/SQL para capturar ROWIDs
		const preparedQuery = prepareQueryAndReplacements(rawQuery, values);

		// Verifica se é um bloco PL/SQL ou tem comentários
		const isPLSQL =
			rawQuery.trim().toUpperCase().startsWith('DECLARE') ||
			rawQuery.trim().toUpperCase().startsWith('BEGIN');
		const hasComments = rawQuery.includes('--') || rawQuery.includes('/*');

		// Garante que a formatação seja preservada para blocos PL/SQL e queries com comentários
		if (isPLSQL || hasComments) {
			// Mantém a formatação, mas normaliza as quebras de linha
			preparedQuery.query = preparedQuery.query.replace(/\r\n/g, '\n');
		}

		if ((nodeOptions.nodeVersion as number) >= 2.3) {
			const parsedNumbers = preparedQuery.values.map((value) => {
				return Number(value) ? Number(value) : value;
			});
			preparedQuery.values = parsedNumbers;
		}

		queries.push(preparedQuery);
	}
	returnData = await runQueries(queries);

	let processedData: INodeExecutionData[] = [];

	for (let i = 0; i < returnData.length; i++) {
		const queryResult = returnData[i];

		if (!queryResult || !queryResult.json || Object.keys(queryResult.json).length === 0) {
			processedData.push({
				json: {
					not_found: true,
					//query: queries[i].query, // Optional: include the query for reference
				},
			});
		} else {
			// Adiciona todos os resultados ao processedData
			processedData.push({
				json: queryResult.json, // Certifique-se de que isso é um INodeExecutionData
			});
		}
	}

	return processedData;
}

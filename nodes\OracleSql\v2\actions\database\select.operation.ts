import type {
	IDataObject,
	IExecuteFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';

import type {
	QueryRunner,
	QueryValues,
	QueryWithValues,
	SortRule,
	WhereClause,
} from '../../helpers/interfaces';

import { addSortRules, addWhereClauses, escapeSqlIdentifier } from '../../helpers/utils';

import { updateDisplayOptions } from '../../../../../utils/utilities';
import {
	combineConditionsCollection,
	optionsCollection,
	selectRowsFixedCollection,
	sortFixedCollection,
} from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Return All',
		name: 'returnAll',
		type: 'boolean',
		default: false,
		description: 'Whether to return all results or only up to a given limit',
		displayOptions: {
			show: {
				resource: ['event'],
				operation: ['getAll'],
			},
		},
	},
	{
		displayName: 'Limit',
		name: 'limit',
		type: 'number',
		default: 50,
		description: 'Max number of results to return',
		typeOptions: {
			minValue: 1,
		},
		displayOptions: {
			show: {
				returnAll: [false],
			},
		},
	},
	selectRowsFixedCollection,
	combineConditionsCollection,
	sortFixedCollection,
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['select'],
	},
	hide: {
		table: [''],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const queries: QueryWithValues[] = [];

	// Get the limit parameter once
	const returnAll = this.getNodeParameter('returnAll', 0, false);
	const limit = !returnAll ? this.getNodeParameter('limit', 0, 50) as number : undefined;

	// Only process the items we need based on limit
	const itemsToProcess = limit ? inputItems.slice(0, limit) : inputItems;

	// Build queries only for the items we'll process
	for (let i = 0; i < itemsToProcess.length; i++) {
		const table = this.getNodeParameter('table', i, undefined, {
			extractValue: true,
		}) as string;

		const outputColumns = this.getNodeParameter('options.outputColumns', i, ['*']) as string[];
		const selectDistinct = this.getNodeParameter('options.selectDistinct', i, false) as boolean;

		let query = '';
		const SELECT = selectDistinct ? 'SELECT DISTINCT' : 'SELECT';

		if (outputColumns.includes('*')) {
			query = `${SELECT} * FROM ${escapeSqlIdentifier(table)}`;
		} else {
			const escapedColumns = outputColumns.map(escapeSqlIdentifier).join(', ');
			query = `${SELECT} ${escapedColumns} FROM ${escapeSqlIdentifier(table)}`;
		}

		let values: QueryValues = [];

		// Modificação na captura dos where clauses
		const whereParameter = this.getNodeParameter('where', i, {}) as IDataObject;
		const whereClauses = Array.isArray(whereParameter.values)
			? whereParameter.values.map((clause: any) => ({
					column: clause.column,
					condition: clause.condition,
					value: clause.value?.value !== undefined ? clause.value.value : clause.value,
				}))
			: [];

		const combineConditions = this.getNodeParameter('combineConditions', i, 'AND') as string;

		[query, values] = addWhereClauses(
			this.getNode(),
			i,
			query,
			whereClauses as WhereClause[],
			values,
			combineConditions,
		);

		const sortRules =
			((this.getNodeParameter('sort', i, []) as IDataObject).values as SortRule[]) || [];

		[query, values] = addSortRules(query, sortRules, values);

		// Don't add FETCH FIRST to the query - let Oracle handle all results
		// and we'll filter them after execution

		queries.push({ query, values });
	}

	// Execute the queries
	returnData = await runQueries(queries);

	// If not returnAll and we have more results than the limit, trim the results
	if (!returnAll && limit && returnData.length > limit) {
		returnData = returnData.slice(0, limit);
	}

	return returnData;
}

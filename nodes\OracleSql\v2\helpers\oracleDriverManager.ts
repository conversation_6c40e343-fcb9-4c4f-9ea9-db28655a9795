import { IDataObject } from 'n8n-workflow';
import { oracleEvents, DRIVER_MODE_CHANGE_EVENT } from './interfaces';
import { requiresThickMode } from '../config/oracleConfig';

/**
 * Singleton class to manage the Oracle driver instance
 * This ensures that all parts of the application use the same instance
 * and can be notified when the driver mode changes
 */
class OracleDriverManager {
	private static instance: OracleDriverManager;
	private oracledb: any;
	private currentDriverMode: 'thin' | 'thick' = 'thin';
	private isInitializing = false;
	private activeConnections: Set<any> = new Set();
	private activePoolConnections: Map<string, any> = new Map();

	private constructor() {
		// Load the Oracle driver
		this.oracledb = require('oracledb');

		// Set initial driver mode based on environment variable
		this.currentDriverMode =
			process.env.NODE_ORACLEDB_DRIVER_MODE?.toLowerCase() === 'thick' ? 'thick' : 'thin';

		// Initialize the driver with the initial mode
		this.configureDriverMode(this.currentDriverMode);

		// Listen for driver mode change events
		oracleEvents.on(DRIVER_MODE_CHANGE_EVENT, async (driverMode: 'thin' | 'thick') => {
			await this.handleDriverModeChange(driverMode);
		});

		// console.log(`🔌 Oracle Driver Manager initialized in ${this.currentDriverMode} mode`);
	}

	/**
	 * Get the singleton instance
	 */
	public static getInstance(): OracleDriverManager {
		if (!OracleDriverManager.instance) {
			OracleDriverManager.instance = new OracleDriverManager();
		}
		return OracleDriverManager.instance;
	}

	/**
	 * Get the Oracle driver instance
	 */
	public getDriver(): any {
		return this.oracledb;
	}

	/**
	 * Get the current driver mode
	 */
	public getDriverMode(): 'thin' | 'thick' {
		return this.currentDriverMode;
	}

	/**
	 * Register a connection to be tracked
	 * This allows us to close all connections when changing driver mode
	 */
	public registerConnection(connection: any): void {
		this.activeConnections.add(connection);
	}

	/**
	 * Unregister a connection when it's closed
	 */
	public unregisterConnection(connection: any): void {
		this.activeConnections.delete(connection);
	}

	/**
	 * Register a pool connection
	 */
	public registerPoolConnection(key: string, pool: any): void {
		this.activePoolConnections.set(key, pool);
	}

	/**
	 * Unregister a pool connection
	 */
	public unregisterPoolConnection(key: string): void {
		this.activePoolConnections.delete(key);
	}

	/**
	 * Get a pool connection by key
	 */
	public getPoolConnection(key: string): any {
		return this.activePoolConnections.get(key);
	}

	/**
	 * Close all active connections
	 * This is necessary when changing driver mode
	 */
	private async closeAllConnections(): Promise<void> {
		console.log(`🔄 Closing all active Oracle connections (${this.activeConnections.size})...`);

		// Close all individual connections
		const connectionPromises = Array.from(this.activeConnections).map(async (connection) => {
			try {
				await connection.close();
				this.activeConnections.delete(connection);
			} catch (error) {
				console.error('Error closing connection:', error.message);
			}
		});

		// Close all pool connections
		const poolPromises = Array.from(this.activePoolConnections.entries()).map(
			async ([key, pool]) => {
				try {
					await pool.close(10); // 10 seconds timeout
					this.activePoolConnections.delete(key);
				} catch (error) {
					console.error(`Error closing pool ${key}:`, error.message);
					// Try to force terminate if normal close fails
					try {
						await pool.terminate();
						this.activePoolConnections.delete(key);
					} catch (termError) {
						console.error(`Error terminating pool ${key}:`, termError.message);
					}
				}
			},
		);

		// Wait for all connections to close
		await Promise.all([...connectionPromises, ...poolPromises]);
		console.log('✅ All Oracle connections closed');
	}

	/**
	 * Handle driver mode change
	 * This is called when the driver mode is changed via the event system
	 */
	private async handleDriverModeChange(newDriverMode: 'thin' | 'thick'): Promise<void> {
		// If already in the requested mode, do nothing
		if (this.currentDriverMode === newDriverMode) {
			// console.log(`🔄 Driver already in ${newDriverMode} mode`);
			return;
		}

		console.log(`🔄 Changing Oracle driver mode from ${this.currentDriverMode} to ${newDriverMode}`);

		// Close all active connections before changing mode
		await this.closeAllConnections();

		// Change the driver mode
		await this.configureDriverMode(newDriverMode);
	}

	/**
	 * Configure the driver mode
	 * This is the core function that actually changes the driver mode
	 */
	private async configureDriverMode(driverMode: 'thin' | 'thick'): Promise<boolean> {
		// If already initializing, wait for it to complete
		if (this.isInitializing) {
			while (this.isInitializing) {
				await new Promise((resolve) => setTimeout(resolve, 100));
			}
			return this.currentDriverMode === driverMode;
		}

		this.isInitializing = true;

		try {
			// If already in the requested mode, do nothing
			if (
				(driverMode === 'thin' && this.oracledb.thin === true) ||
				(driverMode === 'thick' && this.oracledb.thin === false)
			) {
				console.log(`🔄 Driver already in ${driverMode} mode`);
				return true;
			}

			if (driverMode === 'thick') {
				// If the mode is thick, initialize the Oracle Client
				console.log('🔄 Configuring Oracle Client for thick mode');

				try {
					// When a thin connection already exists, we need to reload the module
					if (this.oracledb.thin === true) {
						console.log('⚠️ Detected existing thin connection. Reloading oracledb module...');

						// Clear the module from require cache
						delete require.cache[require.resolve('oracledb')];

						// Reload the module
						this.oracledb = require('oracledb');

						// Initialize the Oracle Client
						this.oracledb.initOracleClient({
							libDir: process.env.LD_LIBRARY_PATH,
							configDir: process.env.OCI_LIB_DIR,
						});

						console.log('✅ Oracle module reloaded and initialized in thick mode');
					} else {
						// If no thin connection exists, we can initialize normally
						this.oracledb.initOracleClient({
							libDir: process.env.LD_LIBRARY_PATH,
							configDir: process.env.OCI_LIB_DIR,
						});
						console.log('✅ Oracle Client initialized in thick mode');
					}

					this.currentDriverMode = 'thick';
					return true;
				} catch (error) {
					console.error('❌ Error initializing Oracle Client in thick mode:', error.message);
					console.warn('⚠️ Continuing in thin mode (default)');
					this.currentDriverMode = 'thin';
					return false;
				}
			} else {
				// In thin mode, we don't need to call initOracleClient()
				// The driver uses thin mode by default
				console.log('🔄 Using Oracle Client in thin mode (default)');

				// We still need to reload the module if we're switching from thick to thin
				if (this.oracledb.thin === false) {
					console.log('⚠️ Detected existing thick connection. Reloading oracledb module...');

					// Clear the module from require cache
					delete require.cache[require.resolve('oracledb')];

					// Reload the module (it will be in thin mode by default)
					this.oracledb = require('oracledb');

					console.log('✅ Oracle module reloaded in thin mode');
				}

				this.currentDriverMode = 'thin';
				return true;
			}
		} finally {
			this.isInitializing = false;
		}
	}

	/**
	 * Check if the database requires thick mode
	 */
	public requiresThickMode(connectString: string): boolean {
		return requiresThickMode(connectString);
	}

	/**
	 * Create a connection pool
	 */
	public async createPool(credentials: any, options?: IDataObject): Promise<any> {
		const requestedDriverMode = credentials.driverMode || 'thin';

		// If the requested driver mode is different from the current mode, change it
		if (requestedDriverMode !== this.currentDriverMode) {
			// Emit the driver mode change event
			oracleEvents.emit(DRIVER_MODE_CHANGE_EVENT, requestedDriverMode);

			// Wait for the driver mode to change
			while (this.currentDriverMode !== requestedDriverMode) {
				await new Promise((resolve) => setTimeout(resolve, 100));
			}
		}

		// Create the pool with the current driver
		const poolConfig = {
			...credentials,
			...options,
			poolIncrement: 1,
			poolMax: 10,
			poolMin: 1,
			enableStatistics: true,
			events: true,
			poolTimeout: 60, // Timeout in seconds
			queueTimeout: 60000, // Queue timeout in milliseconds
		};

		const pool = await this.oracledb.createPool(poolConfig);

		// Register the pool
		const poolKey = credentials.connectString || `${credentials.host}:${credentials.port || 1521}/${credentials.sid}`;
		this.registerPoolConnection(poolKey, pool);

		return pool;
	}
}

// Export the singleton instance
export const oracleDriverManager = OracleDriverManager.getInstance();

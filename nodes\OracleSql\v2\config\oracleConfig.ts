import { IDataObject } from 'n8n-workflow';
import OracleDB from 'oracledb';

// Configuração padrão do Oracle
export const DEFAULT_DRIVER_MODE = 'thin';

/**
 * Configura o modo do driver Oracle (thin ou thick)
 * @param options Opções de configuração
 */
export function configureOracleDriver(options?: IDataObject): void {
	try {
		// Verifica se o modo do driver foi especificado nas opções
		const driverMode =
			(options?.driverMode as string) ||
			process.env.NODE_ORACLEDB_DRIVER_MODE ||
			DEFAULT_DRIVER_MODE;

		// Se o modo for 'thick', inicializa o cliente Oracle
		if (driverMode.toLowerCase() === 'thick') {
			// Se o Oracle Client já foi inicializado, não faz nada
			if (OracleDB.thin === false) {
				console.log('Oracle Client já está inicializado no modo thick');
				return;
			}

			// Inicializa o Oracle Client no modo thick
			console.log('Inicializando Oracle Client no modo thick...');
			// Não fazemos a inicialização aqui, deixamos para o usuário chamar initOracleClient()
			console.log(
				'⚠️ Para usar o modo thick, você deve chamar oracledb.initOracleClient() em seu código',
			);
		} else {
			// Configura o modo thin (padrão)
			console.log('Usando Oracle Client no modo thin (padrão)');

			// No modo thin, não é necessário chamar initOracleClient()
			// O driver já usa o modo thin por padrão
		}
	} catch (error) {
		console.error('Erro ao configurar o driver Oracle:', error);
		throw error;
	}
}

/**
 * Verifica se o banco de dados é antigo e requer o modo thick
 * @param connectString String de conexão com o banco de dados
 * @returns true se o banco de dados for antigo e precisar do modo thick
 */
export function requiresThickMode(connectString: string): boolean {
	// Verifica se o modo foi explicitamente definido como thick
	if (process.env.NODE_ORACLEDB_DRIVER_MODE?.toLowerCase() === 'thick') {
		return true;
	}

	// Verifica se a string de conexão contém indicadores de banco de dados antigo
	// Exemplos: versões anteriores a 12c, formatos específicos, etc.
	const oldDatabasePatterns = [
		/9i/i,
		/10g/i,
		/11g/i,
		/xe11/i,
		/oracle9/i,
		/oracle10/i,
		/oracle11/i,
	];

	return oldDatabasePatterns.some((pattern) => pattern.test(connectString));
}

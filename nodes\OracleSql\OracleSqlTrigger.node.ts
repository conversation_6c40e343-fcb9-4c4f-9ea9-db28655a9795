import {
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	ITriggerFunctions,
	ITriggerResponse,
	NodeConnectionType,
	NodeOperationError,
} from 'n8n-workflow';
import {
	optionsCollection,
	selectRowsFixedCollection,
	tableRLC,
} from './v2/actions/common.descriptions';
import { OracleSqlNodeCredentials } from './v2/helpers/interfaces';
import { oracleDriverManager } from './v2/helpers/oracleDriverManager';
import { listSearch, loadOptions } from './v2/methods';
import { createPool } from './v2/transport';

// Get the Oracle driver from the manager
const oracledb = oracleDriverManager.getDriver();

export class OracleSqlTrigger implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Oracle Trigger',
		name: 'oracleSqlTrigger',
		icon: { light: 'file:oracle.svg', dark: 'file:oracle.dark.svg' },
		group: ['trigger'],
		version: 1,
		description: 'Listens to Oracle SQL events',
		defaults: {
			name: 'Oracle Trigger',
		},
		inputs: [],
		outputs: [NodeConnectionType.Main],
		credentials: [
			{
				name: 'oracleSql',
				required: true,
				displayName: 'Oracle SQL Credentials',
			},
		],
		properties: [
			{
				displayName:
					'⚠️ Requer o modo <strong>Thick</strong>. Usa InstantClient.',
				name: 'thickModeNotice',
				type: 'notice',
				default: '',
			},
			tableRLC,
			{
				displayName: 'Operation Type',
				name: 'operationType',
				type: 'options',
				default: 'all',
				options: [
					{
						name: 'All Operations',
						value: 'all',
						description: 'Monitor INSERT, UPDATE, and DELETE operations',
					},
					{
						name: 'Insert',
						value: 'INSERT',
						description: 'Monitor only INSERT operations',
					},
					{
						name: 'Update',
						value: 'UPDATE',
						description: 'Monitor only UPDATE operations',
					},
					{
						name: 'Delete',
						value: 'DELETE',
						description: 'Monitor only DELETE operations',
					},
				],
				description: 'The type of database operation to monitor',
			},
			{
				displayName: 'Column',
				name: 'outputColumns',
				type: 'options',
				required: true,
				description:
					'Choose the column to monitor for changes. The trigger will only fire when this column is modified.',
				typeOptions: {
					loadOptionsMethod: 'getColumnsMultiOptions',
					loadOptionsDependsOn: ['table.value'],
				},
				default: '',
				hint: 'Select ROWID to monitor any change in the table.',
			},
			selectRowsFixedCollection,
			optionsCollection,
		],
	};

	methods = {
		loadOptions,
		listSearch,
	};

	async trigger(this: ITriggerFunctions): Promise<ITriggerResponse> {
		let connection: any; // Use any instead of oracledb.Connection
		const subscriptionName = 'oracleCQNSub';

		try {
			const credentials = (await this.getCredentials('oracleSql')) as OracleSqlNodeCredentials;

			// Verifica se o banco de dados requer o modo thick
			if (
				oracleDriverManager.requiresThickMode(credentials.connectString) &&
				oracledb.thin !== false
			) {
				throw new NodeOperationError(
					this.getNode(),
					`Este banco de dados requer o modo thick. Altere o modo do driver para "thick" nas credenciais.`,
				);
			}

			const pool = await createPool.call(this, credentials, { events: true });

			connection = await pool.getConnection();
			console.log('Oracle connection established.');

			// Obter o parâmetro da tabela e extrair o valor correto
			const tableParam = this.getNodeParameter('table') as { mode: string; value: string } | string;
			const tableName = typeof tableParam === 'object' ? tableParam.value : tableParam;
			// Obter e validar a coluna
			let columnName = this.getNodeParameter('outputColumns') as string;

			// Verificar se a coluna está vazia
			if (!columnName || columnName.trim() === '') {
				columnName = 'ROWID'; // Usar ROWID como coluna padrão
			}
			// Obter as condições WHERE da nova estrutura
			const whereValues = this.getNodeParameter('where.values', []) as Array<{
				column: string;
				condition: string;
				value: string;
			}>;

			// Construir a condição WHERE
			let condition = '1=1'; // Condição padrão que sempre é verdadeira

			if (whereValues && whereValues.length > 0) {
				const conditions = whereValues.map((whereItem) => {
					const { column, condition: operator, value } = whereItem;

					// Escapar o nome da coluna
					const escapedColumn = column.includes('.') ? column : `"${column}"`;

					// Tratar operadores especiais
					if (['IS NULL', 'IS NOT NULL'].includes(operator)) {
						return `${escapedColumn} ${operator}`;
					}

					// Tratar o operador 'equal'
					const sqlOperator = operator === 'equal' ? '=' : operator;

					// Escapar o valor se for uma string
					let escapedValue = value;
					if (isNaN(Number(value)) && !value.startsWith(':')) {
						escapedValue = `'${value.replace(/'/g, "''")}'`;
					}

					return `${escapedColumn} ${sqlOperator} ${escapedValue}`;
				});

				// Usamos AND para combinar as condições
				condition = conditions.join(' AND ');
			}

			// Não adicionamos condições extras para a coluna monitorada
			// pois isso pode interferir na captura de alterações

			// Verificar se o nome da tabela está vazio
			if (!tableName || tableName.trim() === '') {
				throw new Error(
					'Nome da tabela não pode estar vazio. Por favor, selecione uma tabela válida.',
				);
			}

			// Escapar o nome da tabela para evitar erros de sintaxe
			const escapedTableName = tableName.includes('.') ? tableName : `"${tableName}"`;

			// Construir a consulta SQL com todas as validações
			// Usar uma consulta simples que retorna apenas o ROWID
			const query = `SELECT ROWID FROM ${escapedTableName} WHERE ${condition}`;

			// Validar a consulta SQL final
			if (!query.includes('WHERE') || query.endsWith('WHERE')) {
				throw new Error('Consulta SQL inválida. A cláusula WHERE está incompleta.');
			}

			console.log('Oracle trigger query:', query);

			const onNotification = async (message: any) => {
				console.log('CQN Notification received:', JSON.stringify(message, null, 2));
				console.log('Coluna monitorada:', columnName);

				if (message.tables && Array.isArray(message.tables)) {
					for (const table of message.tables) {
						if (table.rows && Array.isArray(table.rows)) {
							for (const row of table.rows) {
								// Verificar se a linha alterada corresponde à coluna monitorada
								console.log('Linha alterada:', JSON.stringify(row, null, 2));
								console.log('Colunas alteradas:', row.changedColumns);
								// Verificar se a linha alterada atende às condições WHERE
								console.log('Verificando condições WHERE:', whereValues);

								// Obter os dados atuais da linha para verificar as condições WHERE e obter os valores atuais
								try {
									// Consulta para obter os dados atuais da linha
									const rowQuery = `SELECT * FROM ${escapedTableName} WHERE ROWID = :1`;
									const rowResult = await connection.execute(rowQuery, [row.rowid], {
										outFormat: oracledb.OUT_FORMAT_OBJECT,
									});

									if (rowResult.rows && rowResult.rows.length > 0) {
										const rowData = rowResult.rows[0] as Record<string, any>;
										console.log('Dados atuais da linha:', rowData);

										// Verificar se a linha atende às condições WHERE
										let matchesConditions = true;
										if (whereValues && whereValues.length > 0) {
											for (const whereItem of whereValues) {
												const { column, condition: operator, value } = whereItem;
												const columnValue = rowData[column.toUpperCase()];
												console.log(`Verificando condição: ${column} ${operator} ${value}`);
												console.log(`Valor da coluna: ${columnValue}`);

												// Verificar a condição
												let conditionMet = false;

												if (operator === 'IS NULL') {
													conditionMet = columnValue === null;
												} else if (operator === 'IS NOT NULL') {
													conditionMet = columnValue !== null;
												} else {
													const compareValue = isNaN(Number(value)) ? value : Number(value);

													switch (operator) {
														case 'equal':
															conditionMet = columnValue === compareValue;
															break;
														case '!=':
															conditionMet = columnValue !== compareValue;
															break;
														case 'LIKE':
															// Implementação simples de LIKE
															const pattern = String(compareValue)
																.replace(/%/g, '.*')
																.replace(/_/g, '.');
															const regex = new RegExp(`^${pattern}$`, 'i');
															conditionMet = regex.test(String(columnValue));
															break;
														case '>':
															conditionMet = columnValue > compareValue;
															break;
														case '<':
															conditionMet = columnValue < compareValue;
															break;
														case '>=':
															conditionMet = columnValue >= compareValue;
															break;
														case '<=':
															conditionMet = columnValue <= compareValue;
															break;
														default:
															conditionMet = false;
													}
												}

												console.log(`Condição atendida: ${conditionMet}`);

												if (!conditionMet) {
													matchesConditions = false;
													break;
												}
											}
										}

										if (!matchesConditions) {
											console.log(`Ignorando notificação: linha não atende às condições WHERE`);
											continue;
										}

										// Determinar quais colunas foram alteradas
										let detectedChangedColumns: string[] = [];

										// Se o Oracle não forneceu as colunas alteradas, tentamos detectar
										if (!row.changedColumns || row.changedColumns.length === 0) {
											// Para operações de UPDATE, consideramos a coluna monitorada como alterada
											if ((row.operation === 2 || row.operation === 4) && columnName !== 'ROWID') {
												detectedChangedColumns = [columnName];
											}
										} else {
											detectedChangedColumns = row.changedColumns;
										}

										// Filtrar os dados para incluir apenas os campos alterados
										const changedData: Record<string, any> = {};
										for (const column of detectedChangedColumns) {
											if (column !== 'ROWID' && rowData[column.toUpperCase()] !== undefined) {
												changedData[column] = rowData[column.toUpperCase()];
											}
										}

										// Formatar o timestamp no formato dd/mm/yyyy com o horário de São Paulo
										const now = new Date();
										const spTimeOptions = { timeZone: 'America/Sao_Paulo' };
										const day = now
											.toLocaleDateString('pt-BR', spTimeOptions)
											.split('/')[0]
											.padStart(2, '0');
										const month = now
											.toLocaleDateString('pt-BR', spTimeOptions)
											.split('/')[1]
											.padStart(2, '0');
										const year = now.toLocaleDateString('pt-BR', spTimeOptions).split('/')[2];
										const hours = now
											.toLocaleTimeString('pt-BR', spTimeOptions)
											.split(':')[0]
											.padStart(2, '0');
										const minutes = now
											.toLocaleTimeString('pt-BR', spTimeOptions)
											.split(':')[1]
											.padStart(2, '0');
										const seconds = now
											.toLocaleTimeString('pt-BR', spTimeOptions)
											.split(':')[2]
											.padStart(2, '0');
										const formattedTimestamp = `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;

										// Verificar se o tipo de operação corresponde ao selecionado
										const selectedOperationType = this.getNodeParameter('operationType') as string;

										// Mapear o código de operação do Oracle para o tipo de operação selecionado
										let currentOperation = '';
										if (row.operation === 1) {
											currentOperation = 'INSERT';
										} else if (row.operation === 2 || row.operation === 4) {
											currentOperation = 'UPDATE';
										} else if (row.operation === 3) {
											currentOperation = 'DELETE';
										} else {
											currentOperation = `UNKNOWN (${row.operation})`;
										}

										console.log(
											`Operação atual: ${currentOperation}, Operação selecionada: ${selectedOperationType}`,
										);

										if (
											selectedOperationType !== 'all' &&
											currentOperation !== selectedOperationType
										) {
											console.log(
												`Ignorando notificação: operação ${currentOperation} não corresponde ao tipo selecionado ${selectedOperationType}`,
											);
											continue;
										}

										// Criar dados de execução com informações úteis
										const executionData: INodeExecutionData = {
											json: {
												rowId: row.rowid,
												tableName: table.name,
												operation: currentOperation || `UNKNOWN (${row.operation})`,
												changedColumns: detectedChangedColumns,
												monitoredColumn: columnName,
												timestamp: formattedTimestamp,
												changedData: changedData, // Incluir apenas os dados dos campos alterados
											},
										};
										this.emit([this.helpers.returnJsonArray([executionData])]);
									}
								} catch (error) {
									console.error('Erro ao processar notificação:', error);

									// Formatar o timestamp no formato dd/mm/yyyy com o horário de São Paulo
									const now = new Date();
									const spTimeOptions = { timeZone: 'America/Sao_Paulo' };
									const day = now
										.toLocaleDateString('pt-BR', spTimeOptions)
										.split('/')[0]
										.padStart(2, '0');
									const month = now
										.toLocaleDateString('pt-BR', spTimeOptions)
										.split('/')[1]
										.padStart(2, '0');
									const year = now.toLocaleDateString('pt-BR', spTimeOptions).split('/')[2];
									const hours = now
										.toLocaleTimeString('pt-BR', spTimeOptions)
										.split(':')[0]
										.padStart(2, '0');
									const minutes = now
										.toLocaleTimeString('pt-BR', spTimeOptions)
										.split(':')[1]
										.padStart(2, '0');
									const seconds = now
										.toLocaleTimeString('pt-BR', spTimeOptions)
										.split(':')[2]
										.padStart(2, '0');
									const formattedTimestamp = `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;

									// Mesmo com erro, emitir dados básicos
									const executionData: INodeExecutionData = {
										json: {
											rowId: row.rowid,
											tableName: table.name,
											operation:
												row.operation === 4
													? 'UPDATE'
													: row.operation === 1
														? 'INSERT'
														: row.operation === 2
															? 'UPDATE'
															: row.operation === 3
																? 'DELETE'
																: `UNKNOWN (${row.operation})`,
											changedColumns: [],
											monitoredColumn: columnName,
											timestamp: formattedTimestamp,
											error: (error as Error).message,
										},
									};
									this.emit([this.helpers.returnJsonArray([executionData])]);
								}
							}
						}
					}
				} else {
					console.warn('No tables information found in the notification message.');
				}
			};

			// Usar a opção SUBSCR_QOS_ROWIDS para capturar alterações

			// Configuração simplificada do Oracle CQN
			await connection.subscribe(subscriptionName, {
				sql: query,
				callback: onNotification,
				clientInitiated: true,
				timeout: 600,
				qos: oracledb.SUBSCR_QOS_ROWIDS,
			});
			console.log('Subscription created successfully.');

			const closeFunction = async () => {
				try {
					await connection.unsubscribe(subscriptionName);
					await connection.close();
					console.log('❌ Oracle connection closed.');
				} catch (error) {
					throw new NodeOperationError(
						this.getNode(),
						`Error closing Oracle connection: ${(error as Error).message}`,
					);
				}
			};

			const manualTriggerFunction = async () => {
				await new Promise<void>((resolve, reject) => {
					const timeoutHandler = setTimeout(() => {
						reject(new Error('No data received within 30 seconds.'));
					}, 30000);

					setTimeout(() => {
						clearTimeout(timeoutHandler);
						resolve();
					}, 1000);
				});
			};

			return {
				closeFunction,
				manualTriggerFunction: this.getMode() === 'manual' ? manualTriggerFunction : undefined,
			};
		} catch (error) {
			console.error('Error setting up Oracle trigger:', error);
			throw new NodeOperationError(
				this.getNode(),
				`Error setting up Oracle trigger: ${(error as Error).message}`,
			);
		}
	}
}

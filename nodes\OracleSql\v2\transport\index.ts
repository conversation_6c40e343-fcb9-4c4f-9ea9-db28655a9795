import type {
	ICredentialTestFunctions,
	IDataObject,
	IExecuteFunctions,
	ILoadOptionsFunctions,
} from 'n8n-workflow';
import {
	DRIVER_MODE_CHANGE_EVENT,
	OracleSqlNodeCredentials,
	OracleSqlPool,
	oracleEvents,
} from '../helpers/interfaces';
import { oracleDriverManager } from '../helpers/oracleDriverManager';

// Get the Oracle driver from the manager
const OracleDB = oracleDriverManager.getDriver();

// Variáveis de controle
let poolInstance: OracleSqlPool | null = null;
let hasLogged = false;
let isSessionConfigured = false; // Variável para controlar configuração da sessão
let lastCredentials: OracleSqlNodeCredentials | null = null; // Armazena as últimas credenciais usadas

export async function configureOracleSession(connection: any) {
	if (isSessionConfigured) return;

	const commands = [
		"ALTER SESSION SET NLS_DATE_FORMAT = 'DD/MM/YYYY HH24:MI:SS'",
		"ALTER SESSION SET NLS_TIMESTAMP_TZ_FORMAT = 'DD/MM/YYYY HH24:MI:SS.FF TZH:TZM'",
		"ALTER SESSION SET NLS_TIMESTAMP_FORMAT = 'DD/MM/YYYY HH24:MI:SS.FF'",
		"ALTER SESSION SET NLS_NUMERIC_CHARACTERS = '.,'",
		'ALTER SESSION SET NLS_NCHAR_CONV_EXCP = FALSE',
		"ALTER SESSION SET TIME_ZONE = '-03:00'",
	];

	try {
		for (const command of commands) {
			await connection.execute(command);
		}
		isSessionConfigured = true;
		console.log('✅ Sessão Oracle configurada com sucesso');
	} catch (error) {
		console.error('❌ Erro ao configurar sessão Oracle:', error.message);
		throw error;
	}
}

export async function getConnection(pool: any) {
	if (!pool || pool.status !== OracleDB.POOL_STATUS_OPEN) {
		throw new Error('Pool não está disponível ou foi fechado');
	}

	const connection = await pool.getConnection();
	// Register the connection with the driver manager
	oracleDriverManager.registerConnection(connection);

	// Configure the session
	await configureOracleSession(connection);

	return connection;
}

// Função para criar pool com lock
async function initializePool(
	credentials: OracleSqlNodeCredentials,
	options?: IDataObject,
): Promise<OracleSqlPool> {
	// Verifica se o banco de dados requer o modo thick
	if (oracleDriverManager.requiresThickMode(credentials.connectString)) {
		// Verifica se estamos no modo thin
		if (OracleDB.thin !== false) {
			console.warn(`Banco de dados antigo detectado na conexão: ${credentials.connectString}`);
			console.warn('Este banco de dados pode requerer o modo thick.');
			console.warn(
				'Altere o modo do driver para "thick" nas credenciais ou configure NODE_ORACLEDB_DRIVER_MODE=thick no ambiente.',
			);
		}
	}

	// Garante que a string de conexão esteja no formato correto
	if (credentials.host && credentials.sid) {
		// Se temos host e sid, construímos a string de conexão no formato Easy Connect
		credentials.connectString = `${credentials.host}:${credentials.port || 1521}/${credentials.sid}`;
	}

	if (!hasLogged) {
		console.log(`
████████╗███████╗░█████╗░██╗░░██╗  ███╗░░██╗███████╗██████╗░██╗░░░██╗░██████╗
╚══██╔══╝██╔════╝██╔══██╗██║░░██║  ████╗░██║██╔════╝██╔══██╗██║░░░██║██╔════╝
░░░██║░░░█████╗░░██║░░╚═╝███████║  ██╔██╗██║█████╗░░██████╔╝╚██╗░██╔╝╚█████╗░
░░░██║░░░██╔══╝░░██║░░██╗██╔══██║  ██║╚████║██╔══╝░░██╔══██╗░╚████╔╝░░╚═══██╗
░░░██║░░░███████╗╚█████╔╝██║░░██║  ██║░╚███║███████╗██║░░██║░░╚██╔╝░░██████╔╝
░░░╚═╝░░░╚══════╝░╚════╝░╚═╝░░╚═╝  ╚═╝░░╚══╝╚══════╝╚═╝░░╚═╝░░░╚═╝░░░╚═════╝░
www.technervs.com
`);
		console.log('⚙️  Initializing OracleDB connection pool...');
		console.log(
			`🍁 Oracle Client Version: ${OracleDB.oracleClientVersion || 'N/A (Thin Mode)'} 🍪`,
		);
		console.log(`🔑 Driver Mode: ${OracleDB.thin ? 'Thin' : 'Thick'}`);
		hasLogged = true;
	}

	// Use the driver manager to create the pool
	const pool = await oracleDriverManager.createPool(credentials, options);
	poolInstance = pool as OracleSqlPool;
	return pool as OracleSqlPool;
}

/**
 * Compara duas credenciais para verificar se são iguais
 * @param cred1 Primeira credencial
 * @param cred2 Segunda credencial
 * @returns true se as credenciais forem iguais, false caso contrário
 */
function areCredentialsEqual(
	cred1: OracleSqlNodeCredentials | null,
	cred2: OracleSqlNodeCredentials | null,
): boolean {
	if (!cred1 || !cred2) return false;

	// Compara os campos relevantes para a conexão
	return (
		cred1.user === cred2.user &&
		cred1.password === cred2.password &&
		cred1.connectString === cred2.connectString &&
		cred1.host === cred2.host &&
		cred1.port === cred2.port &&
		cred1.sid === cred2.sid &&
		cred1.driverMode === cred2.driverMode
	);
}

export async function createPool(
	this: IExecuteFunctions | ICredentialTestFunctions | ILoadOptionsFunctions,
	credentials: OracleSqlNodeCredentials,
	options?: IDataObject,
): Promise<OracleSqlPool> {
	try {
		// Garante que a string de conexão esteja no formato correto
		if (credentials.host && credentials.sid) {
			// Se temos host e sid, construímos a string de conexão no formato Easy Connect
			credentials.connectString = `${credentials.host}:${credentials.port || 1521}/${credentials.sid}`;
		}

		// Verifica se o modo do driver foi especificado nas credenciais
		const requestedDriverMode = credentials.driverMode || 'thin';

		// Verifica se as credenciais ou o modo do driver mudaram
		const credentialsChanged = !areCredentialsEqual(lastCredentials, credentials);

		// Se as credenciais mudaram ou o pool não existe, fecha o pool atual (se existir)
		if (
			(credentialsChanged || !poolInstance) &&
			poolInstance?.status === OracleDB.POOL_STATUS_OPEN
		) {
			if (credentialsChanged) {
				console.log('🔄 Credenciais alteradas, fechando pool atual...');

				// Se o modo do driver mudou, mostra uma mensagem específica
				if (lastCredentials?.driverMode !== requestedDriverMode) {
					console.log(
						`🔄 Modo do driver alterado de ${lastCredentials?.driverMode || 'thin'} para ${requestedDriverMode}`,
					);

					// Emitir evento de mudança de modo do driver
					oracleEvents.emit(DRIVER_MODE_CHANGE_EVENT, requestedDriverMode);
				}
			}

			await closePool();
		}

		// Se o pool existe e as credenciais não mudaram, reutiliza o pool
		if (poolInstance?.status === OracleDB.POOL_STATUS_OPEN && !credentialsChanged) {
			console.log('🔄 Reutilizando pool existente para:', credentials.sid || 'Oracle');
			return poolInstance as OracleSqlPool;
		}

		// Armazena as novas credenciais e cria um novo pool
		lastCredentials = { ...credentials };
		console.log('🔄 Criando novo pool para:', credentials.sid || 'Oracle');
		return await initializePool(credentials, options);
	} catch (error) {
		console.error('🚫 Error while creating pool:', error);
		poolInstance = null;
		lastCredentials = null;
		throw error;
	}
}

export async function closePool(): Promise<void> {
	if (poolInstance) {
		try {
			// Aumenta o tempo de dreno para 10 segundos e força o fechamento
			console.log('🔄 Iniciando fechamento do pool...');

			// Define um timeout maior para garantir que todas as conexões sejam fechadas
			const closeTimeout = 10; // 10 segundos

			// Tenta fechar o pool normalmente primeiro
			if (poolInstance) {
				// Unregister the pool from the driver manager if it exists
				if (lastCredentials) {
					const poolKey =
						lastCredentials.connectString ||
						`${lastCredentials.host}:${lastCredentials.port || 1521}/${lastCredentials.sid}`;
					oracleDriverManager.unregisterPoolConnection(poolKey);
				}

				await poolInstance.close(closeTimeout);
			}

			poolInstance = null;
			lastCredentials = null;
			isSessionConfigured = false;
			hasLogged = false;
			console.log('🔒 Pool fechado com sucesso');
		} catch (error) {
			if (error.message && error.message.includes('NJS-104')) {
				console.warn('⚠️ Detectadas conexões ativas, forçando fechamento...');
				try {
					// Se falhar, força o término com terminate()
					if (poolInstance) {
						await poolInstance.terminate();
					}
					poolInstance = null;
					lastCredentials = null;
					console.log('🔒 Pool terminado forçadamente');
				} catch (termError) {
					console.error('❌ Erro ao terminar pool:', termError.message);
				}
			} else {
				console.error('❌ Erro ao fechar pool:', error.message);
			}
		}
	}
}

// Tratamento de sinais para fechamento gracioso
process.on('SIGINT', async () => {
	console.log('\n🛑 Recebido SIGINT. Encerrando...');
	await closePool();
	process.exit(0);
});

process.on('SIGTERM', async () => {
	console.log('\n🛑 Recebido SIGTERM. Encerrando...');
	await closePool();
	process.exit(0);
});

// Tratamento de exceções não capturadas
process.on('uncaughtException', async (error) => {
	console.error('❌ Exceção não capturada:', error);
	await closePool();
	process.exit(1);
});

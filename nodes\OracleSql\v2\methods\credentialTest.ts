import type {
	ICredentialTestFunctions,
	ICredentialsDecrypted,
	INodeCredentialTestResult,
} from 'n8n-workflow';

import type { OracleSqlNodeCredentials } from '../helpers/interfaces';
import { oracleDriverManager } from '../helpers/oracleDriverManager';
import { createPool } from '../transport';

// Get the Oracle driver from the manager
const OracleDB = oracleDriverManager.getDriver();

export async function oracleSqlConnectionTest(
	this: ICredentialTestFunctions,
	credential: ICredentialsDecrypted,
): Promise<INodeCredentialTestResult> {
	const credentials = credential.data as OracleSqlNodeCredentials;

	// Verifica se o banco de dados requer o modo thick
	if (oracleDriverManager.requiresThickMode(credentials.connectString) && OracleDB.thin !== false) {
		// Como n8n só aceita 'OK' ou 'Error', usamos 'OK' com uma mensagem de aviso
		return {
			status: 'OK',
			message: `⚠️ AVISO: Banco de dados antigo detectado. Recomendamos alterar o modo do driver para "thick" nas credenciais para melhor compatibilidade.`,
		};
	}

	const pool = await createPool.call(this, credentials);

	try {
		const connection = await pool.getConnection();

		// Verifica a versão do banco de dados
		let databaseVersion = 'Desconhecida';
		try {
			const result = await connection.execute('SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1');
			if (
				result.rows &&
				Array.isArray(result.rows) &&
				result.rows.length > 0 &&
				Array.isArray(result.rows[0])
			) {
				databaseVersion = String(result.rows[0][0] || 'Desconhecida');
			}
		} catch (versionError) {
			console.warn(
				'Não foi possível obter a versão do banco de dados:',
				(versionError as Error).message,
			);
		}

		connection.release();

		return {
			status: 'OK',
			message: `Conexão bem-sucedida! Modo do driver: ${OracleDB.thin ? 'Thin' : 'Thick'}, Versão do banco: ${databaseVersion}`,
		};
	} catch (error) {
		const errorMessage = (error as Error).message;
		// Verifica se o erro pode estar relacionado ao modo thin
		if (OracleDB.thin && (errorMessage.includes('ORA-') || errorMessage.includes('TNS-'))) {
			return {
				status: 'Error',
				message: `${errorMessage}\n\nEste erro pode estar relacionado ao modo thin. Tente alterar o modo do driver para "thick" nas credenciais.`,
			};
		}

		return {
			status: 'Error',
			message: errorMessage,
		};
	} finally {
		await pool.close();
	}
}

import type {
	IDataObject,
	IExecuteFunctions,
	INode,
	INodeExecutionData,
	IPairedItemData,
	NodeExecutionWithMetadata,
} from 'n8n-workflow';

import type {
	OracleSqlPool,
	QueryMode,
	QueryValues,
	QueryWithValues,
	SortRule,
	WhereClause,
} from './interfaces';

import { BATCH_MODE } from './interfaces';

import { NodeOperationError } from 'n8n-workflow';

import { generatePairedItemData } from '../../../../utils/utilities';

import * as oracledb from 'oracledb';

function processItemData(
	this: IExecuteFunctions,
	item: INodeExecutionData,
	index: number,
	result: IDataObject,
): IDataObject {
	const includeOtherFields = this.getNodeParameter('includeOtherFields', index, false) as boolean;

	if (!includeOtherFields) {
		return result;
	}

	const include = this.getNodeParameter('include', index, 'all') as string;
	const newItem: IDataObject = {};

	// Copia todos os campos do item anterior
	if (include === 'all') {
		Object.assign(newItem, item.json);
	}
	// Copia campos selecionados
	else if (include === 'selected') {
		const includeFields = (this.getNodeParameter('includeFields', index, '') as string)
			.split(',')
			.map((field) => field.trim())
			.filter((field) => !!field);

		for (const field of includeFields) {
			if (item.json.hasOwnProperty(field)) {
				newItem[field] = item.json[field];
			}
		}
	}
	// Copia todos exceto os campos excluídos
	else if (include === 'except') {
		Object.assign(newItem, item.json);
		const excludeFields = (this.getNodeParameter('excludeFields', index, '') as string)
			.split(',')
			.map((field) => field.trim())
			.filter((field) => !!field);

		for (const field of excludeFields) {
			delete newItem[field];
		}
	}

	// Combina os campos do resultado com os campos incluídos
	return { ...newItem, ...result };
}

export function escapeSqlIdentifier(identifier: string): string {
	// For Oracle SQL, we don't need to escape normal identifiers
	return identifier;
}

/**
 * Remove acentos de uma string
 * @param text Texto com acentos
 * @returns Texto sem acentos
 */
export function removeAccents(text: string): string {
	return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

/**
 * Sanitiza um valor de texto para inserção no banco de dados
 * - Remove espaços em branco no início e fim
 * - Remove acentos
 * - Converte para maiúsculas (UPPER CASE)
 * - Trata caracteres especiais
 * - Limita o tamanho do campo de acordo com o metadata
 */
export function sanitizeValue(
	value: any,
	metadata?: { maxLength?: number; dataType?: string },
): any {
	// Se o valor for null ou undefined, retorna null
	if (value === null || value === undefined) {
		return null;
	}

	// Se o valor for uma data, retorna a data formatada
	if (value instanceof Date || Object.prototype.toString.call(value) === '[object Date]') {
		return value;
	}

	// Se o valor for um número, retorna o número
	if (typeof value === 'number') {
		return value;
	}

	// Se o valor for um booleano, retorna 1 ou 0
	if (typeof value === 'boolean') {
		return value ? 1 : 0;
	}

	// Se o valor for uma string
	if (typeof value === 'string') {
		// Verifica se é um valor especial (SYSDATE, SELECT, etc)
		const upperValue = value.trim().toUpperCase();
		if (
			upperValue === 'SYSDATE' ||
			upperValue.startsWith('(SELECT') ||
			upperValue.startsWith('SELECT') ||
			upperValue.startsWith('TO_DATE') ||
			upperValue.startsWith('TRUNC(')
		) {
			return value.trim();
		}

		// Verifica se é uma data em formato string
		const datePattern = /^\d{2}\/\d{2}\/\d{4}( \d{2}:\d{2}:\d{2})?$/;
		if (datePattern.test(value)) {
			return value.trim();
		}

		// Sanitiza o valor: remove espaços em branco no início e fim, remove acentos e converte para maiúsculas
		let sanitized = removeAccents(value.trim()).toUpperCase();

		// Limita o tamanho do campo de acordo com o metadata
		if (metadata?.maxLength && metadata.maxLength > 0 && sanitized.length > metadata.maxLength) {
			sanitized = sanitized.substring(0, metadata.maxLength);
		}

		// Escapa aspas simples para evitar SQL injection
		sanitized = sanitized.replace(/'/g, "''");

		return sanitized;
	}

	// Para outros tipos, converte para string, remove acentos e sanitiza
	return removeAccents(String(value).trim()).toUpperCase();
}

// Função auxiliar para modificar blocos PL/SQL e adicionar comandos para capturar ROWIDs
function enhancePLSQLWithRowidCapture(query: string): string {
	// Verifica se é um bloco PL/SQL
	const isPLSQL =
		query.trim().toUpperCase().startsWith('DECLARE') ||
		query.trim().toUpperCase().startsWith('BEGIN');

	if (!isPLSQL) return query;

	// Verifica se o bloco PL/SQL contém operações DML
	const containsInsert = query.toUpperCase().includes('INSERT INTO');
	const containsUpdate = query.toUpperCase().includes('UPDATE');
	const containsDelete = query.toUpperCase().includes('DELETE FROM');

	if (!containsInsert && !containsUpdate && !containsDelete) return query;

	// Extrai o nome da tabela da operação DML
	let tableName = '';
	if (containsInsert) {
		const match = query.match(/INSERT\s+INTO\s+([^\s(]+)/i);
		if (match && match[1]) tableName = match[1];
	} else if (containsUpdate) {
		const match = query.match(/UPDATE\s+([^\s]+)/i);
		if (match && match[1]) tableName = match[1];
	} else if (containsDelete) {
		const match = query.match(/DELETE\s+FROM\s+([^\s]+)/i);
		if (match && match[1]) tableName = match[1];
	}

	if (!tableName) return query;

	// Em vez de tentar modificar o bloco PL/SQL existente, vamos criar um novo bloco completo
	// Isso garante que a sintaxe esteja correta e evita problemas com a modificação do código

	// Extrai a cláusula WHERE para operações UPDATE e DELETE
	let whereClause = '';

	// Tenta extrair a cláusula WHERE usando regex
	if (containsUpdate) {
		const match = query.match(/WHERE\s+[^;]+/i);
		if (match) whereClause = match[0];
	} else if (containsDelete) {
		const match = query.match(/WHERE\s+[^;]+/i);
		if (match) whereClause = match[0];
	}

	// Se não conseguimos extrair a cláusula WHERE usando regex, tenta extrair manualmente
	if (!whereClause) {
		// Divide a query em linhas
		const lines = query.split(/\r?\n/);

		// Procura por linhas que contenham a cláusula WHERE
		for (const line of lines) {
			if (line.trim().toUpperCase().startsWith('WHERE')) {
				// Encontrou uma linha com cláusula WHERE
				whereClause = line.trim().replace(/;$/, '');

				// Se a linha não termina com ponto e vírgula, procura por linhas adicionais
				if (!line.trim().endsWith(';')) {
					const lineIndex = lines.indexOf(line);
					for (let i = lineIndex + 1; i < lines.length; i++) {
						const nextLine = lines[i].trim();
						// Se a próxima linha começa com outra palavra-chave, para de adicionar
						if (
							nextLine.toUpperCase().startsWith('BEGIN') ||
							nextLine.toUpperCase().startsWith('END') ||
							nextLine.toUpperCase().startsWith('INSERT') ||
							nextLine.toUpperCase().startsWith('UPDATE') ||
							nextLine.toUpperCase().startsWith('DELETE')
						) {
							break;
						}
						whereClause += ' ' + nextLine.replace(/;$/, '');
						if (nextLine.endsWith(';')) break;
					}
				}

				break;
			}
		}
	}

	// Extrai a operação DML completa
	let dmlOperation = '';

	// Tenta extrair a operação DML do bloco PL/SQL
	if (containsInsert) {
		// Para INSERT
		const match = query.match(/INSERT\s+INTO\s+[^;]+;/i);
		if (match) dmlOperation = match[0].replace(/;$/, '');
	} else if (containsUpdate) {
		// Para UPDATE
		const match = query.match(/UPDATE\s+[^;]+;/i);
		if (match) dmlOperation = match[0].replace(/;$/, '');
	} else if (containsDelete) {
		// Para DELETE
		const match = query.match(/DELETE\s+FROM\s+[^;]+;/i);
		if (match) dmlOperation = match[0].replace(/;$/, '');
	}

	// Se não conseguimos extrair a operação DML usando regex, tenta extrair manualmente
	if (!dmlOperation) {
		// Divide a query em linhas
		const lines = query.split(/\r?\n/);

		// Procura por linhas que contenham operações DML
		for (const line of lines) {
			if (
				line.trim().toUpperCase().startsWith('UPDATE') ||
				line.trim().toUpperCase().startsWith('INSERT') ||
				line.trim().toUpperCase().startsWith('DELETE')
			) {
				// Encontrou uma linha com operação DML
				dmlOperation = line.trim().replace(/;$/, '');

				// Se a linha não termina com ponto e vírgula, procura por linhas adicionais
				if (!line.trim().endsWith(';')) {
					const lineIndex = lines.indexOf(line);
					for (let i = lineIndex + 1; i < lines.length; i++) {
						const nextLine = lines[i].trim();
						dmlOperation += ' ' + nextLine.replace(/;$/, '');
						if (nextLine.endsWith(';')) break;
					}
				}

				break;
			}
		}
	}

	// Se não conseguimos extrair a operação DML, retorna a query original
	if (!dmlOperation) return query;

	// Cria um novo bloco PL/SQL com código para capturar ROWIDs
	let enhancedQuery = '';

	// Adiciona a declaração de variáveis
	enhancedQuery += `DECLARE
  TYPE rowid_table IS TABLE OF ROWID;
  affected_rowids rowid_table := rowid_table();
  idx NUMBER := 0;
BEGIN
`;

	// Para UPDATE e DELETE, adiciona código para capturar ROWIDs antes da operação
	if ((containsUpdate || containsDelete) && whereClause) {
		enhancedQuery += `  -- Captura ROWIDs antes da operação
  FOR rec IN (SELECT ROWID FROM ${tableName} ${whereClause}) LOOP
    idx := idx + 1;
    affected_rowids.EXTEND;
    affected_rowids(idx) := rec.ROWID;
  END LOOP;

`;
	}

	// Adiciona a operação DML
	enhancedQuery += `  ${dmlOperation};

`;

	// Para INSERT, adiciona código para capturar o ROWID após a operação
	if (containsInsert) {
		enhancedQuery += `  -- Captura o ROWID da linha inserida
  DECLARE
    v_rowid VARCHAR2(20);
  BEGIN
    SELECT ROWID INTO v_rowid FROM ${tableName} WHERE ROWNUM = 1 ORDER BY ROWID DESC;
    idx := 1;
    affected_rowids.EXTEND;
    affected_rowids(idx) := v_rowid;
  EXCEPTION
    WHEN OTHERS THEN
      idx := 0;
  END;

`;
	}

	// Adiciona código para exibir ROWIDs
	enhancedQuery += `  -- Exibe ROWIDs afetados
  DBMS_OUTPUT.PUT_LINE('Número de linhas afetadas: ' || idx);
  FOR i IN 1..affected_rowids.COUNT LOOP
    DBMS_OUTPUT.PUT_LINE('ROWID #' || i || ': ' || affected_rowids(i));
  END LOOP;
END;`;

	return enhancedQuery;
}

export const prepareQueryAndReplacements = (rawQuery: string, replacements?: QueryValues) => {
	// Verifica se é um bloco PL/SQL ou tem comentários
	const isPLSQL =
		rawQuery.trim().toUpperCase().startsWith('DECLARE') ||
		rawQuery.trim().toUpperCase().startsWith('BEGIN');
	const hasComments = rawQuery.includes('--') || rawQuery.includes('/*');

	// Se for PL/SQL ou tiver comentários, preserva a formatação original
	const preserveFormatting = isPLSQL || hasComments;

	// Se for um bloco PL/SQL, melhora-o para capturar ROWIDs automaticamente
	const processedQuery = isPLSQL ? enhancePLSQLWithRowidCapture(rawQuery) : rawQuery;

	if (replacements === undefined) {
		return {
			// Preserva a formatação original para blocos PL/SQL e queries com comentários
			query: preserveFormatting
				? processedQuery.trim()
				: processedQuery
						.replace(/[\r\n]+/g, ' ')
						.replace(/\s+/g, ' ')
						.trim(),
			values: [],
		};
	}

	// Preserva a formatação original para blocos PL/SQL e queries com comentários
	let query: string = preserveFormatting
		? processedQuery.trim()
		: processedQuery
				.replace(/[\r\n]+/g, ' ')
				.replace(/\s+/g, ' ')
				.trim();
	const values: QueryValues = [];

	const regex = /\$(\d+)(?::name)?/g;
	const matches = rawQuery.match(regex) || [];

	for (const match of matches) {
		if (match.includes(':name')) {
			const matchIndex = Number(match.replace('$', '').replace(':name', '')) - 1;
			query = query.replace(match, escapeSqlIdentifier(replacements[matchIndex].toString()));
		} else {
			const matchIndex = Number(match.replace('$', '')) - 1;
			query = query.replace(match, '?');
			values.push(replacements[matchIndex]);
		}
	}

	return { query, values };
};

export function prepareErrorItem(
	item: IDataObject,
	error: IDataObject | NodeOperationError | Error,
	index: number,
) {
	return {
		json: { message: error.message, item: { ...item }, itemIndex: index, error: { ...error } },
		pairedItem: { item: index },
	} as INodeExecutionData;
}

export function parseOracleSqlError(
	this: IExecuteFunctions,
	error: any,
	itemIndex = 0,
	queries?: string[],
) {
	let message: string = error.message;
	const description = `sql: ${error.sql}, code: ${error.code}`;

	if (error.code === 'NJS-003') {
		throw new Error('Pool de conexão inválido ou fechado!');
	}

	if (
		queries?.length &&
		(message || '').toLowerCase().includes('you have an error in your sql syntax')
	) {
		let queryIndex = itemIndex;
		const failedStatement = ((message.split("near '")[1] || '').split("' at")[0] || '').split(
			';',
		)[0];

		if (failedStatement) {
			if (queryIndex === 0 && queries.length > 1) {
				const failedQueryIndex = queries.findIndex((query) => query.includes(failedStatement));
				if (failedQueryIndex !== -1) {
					queryIndex = failedQueryIndex;
				}
			}
			const lines = queries[queryIndex].split('\n');

			const failedLine = lines.findIndex((line) => line.includes(failedStatement));
			if (failedLine !== -1) {
				message = `You have an error in your SQL syntax on line ${
					failedLine + 1
				} near '${failedStatement}'`;
			}
		}
	}

	if ((error?.message as string).includes('ECONNREFUSED')) {
		message = 'Connection refused';
	}

	return new NodeOperationError(this.getNode(), error as Error, {
		message,
		description,
		itemIndex,
	});
}

export function wrapData(data: IDataObject | IDataObject[]): INodeExecutionData[] {
	if (!Array.isArray(data)) {
		return [{ json: data }];
	}
	return data.map((item) => ({
		json: item,
	}));
}

export function prepareOutput(
	response: IDataObject[],
	options: IDataObject,
	statements: string[],
	constructExecutionHelper: (
		inputData: INodeExecutionData[],
		options: {
			itemData: IPairedItemData | IPairedItemData[];
		},
	) => NodeExecutionWithMetadata[],
	itemData: IPairedItemData | IPairedItemData[],
) {
	let returnData: INodeExecutionData[] = [];

	if (options.detailedOutput) {
		response.forEach((entry, index) => {
			const item = {
				sql: statements[index],
				data: entry,
			};

			const executionData = constructExecutionHelper(wrapData(item), {
				itemData,
			});

			returnData = returnData.concat(executionData);
		});
	} else {
		response
			.filter((entry) => Array.isArray(entry))
			.forEach((entry, index) => {
				const executionData = constructExecutionHelper(wrapData(entry), {
					itemData: Array.isArray(itemData) ? itemData[index] : itemData,
				});

				returnData = returnData.concat(executionData);
			});
	}

	if (!returnData.length) {
		if ((options?.nodeVersion as number) < 2.2) {
			returnData.push({ json: { success: true }, pairedItem: itemData });
		} else {
			const isSelectQuery = statements
				.filter((statement) => !statement.startsWith('--'))
				.every((statement) =>
					statement
						.replace(/\/\*.*?\*\//g, '') // remove multiline comments
						.replace(/\n/g, '')
						.toLowerCase()
						.startsWith('select'),
				);

			if (!isSelectQuery) {
				returnData.push({ json: { success: true }, pairedItem: itemData });
			}
		}
	}

	return returnData;
}
const END_OF_STATEMENT = /;(?=(?:[^'\\]|'[^']*?'|\\[\s\S])*?$)/g;

export const splitQueryToStatements = (query: string, filterOutEmpty = true) => {
	const statements = query
		.replace(/\n/g, '')
		.split(END_OF_STATEMENT)
		.map((statement) => statement.trim());
	return filterOutEmpty ? statements.filter((statement) => statement !== '') : statements;
};

// Singleton para gerenciamento de conexão
let sharedConnection: any = null;

export function configureQueryRunner(
	this: IExecuteFunctions,
	options: IDataObject,
	pool: OracleSqlPool,
	items: INodeExecutionData[],
) {
	return async (queries: QueryWithValues[]): Promise<INodeExecutionData[]> => {
		if (queries.length === 0) {
			console.warn('Nenhuma consulta fornecida para execução.');
			return [];
		}

		const returnData: INodeExecutionData[] = [];
		const mode = (options.queryBatching as QueryMode) || BATCH_MODE.SINGLE;
		let connection: any;

		try {
			console.log('Obtendo conexão do pool...');
			connection = await pool.getConnection();

			if (mode === BATCH_MODE.SINGLE) {
				let logQuery = ''; // Variável para registrar a query atual
				try {
					// Verifica se estamos na operação Select (não executeQuery)
					const isSelectOperation = this.getNodeParameter('operation', 0) === 'select';

					// Obtém o valor de limit, se definido (apenas para operação select)
					let limitValue = Infinity;
					let countResults = 0;

					// Só obtém o limit se estamos na operação Select
					if (isSelectOperation) {
						try {
							// Verifica se o parâmetro limit existe e obtém seu valor
							const limitParam = this.getNodeParameter('options.limit', 0, {}) as IDataObject;
							if (limitParam && typeof limitParam === 'number') {
								limitValue = limitParam;
							} else if (limitParam && typeof limitParam === 'string') {
								limitValue = parseInt(limitParam as string, 10);
							} else if (options.limit) {
								// Fallback para options.limit diretamente
								limitValue =
									typeof options.limit === 'number'
										? options.limit
										: parseInt(options.limit as string, 10);
							}

							console.log(`Limit definido: ${limitValue}`);
						} catch (e) {
							// Se não conseguir obter o limit, usa Infinity
							limitValue = Infinity;
							console.log(`Erro ao obter limit: ${e.message}. Usando Infinity.`);
						}
					}

					if (isSelectOperation) {
						// Para operação Select, executamos uma query por item de entrada
						for (let itemIndex = 0; itemIndex < items.length; itemIndex++) {
							// Se já atingimos o limite, paramos de processar mais itens
							if (countResults >= limitValue) {
								console.log(`Limite de ${limitValue} resultados atingido. Parando processamento.`);
								break;
							}

							// Pegamos a query correspondente ao item atual
							// Aqui assumimos que a função que gerou a lista de queries já
							// criou uma query para cada item de entrada
							const { query, values } = queries[itemIndex % queries.length];

							// Remove ponto e vírgula e espaços extras
							const cleanQuery = query.trim().replace(/;$/, '');

							// Atualiza a variável logQuery para fins de debugging
							logQuery = cleanQuery;
							values.forEach((value, idx) => {
								let formattedValue: string;
								if (
									typeof value === 'string' &&
									!value.startsWith('TO_DATE') &&
									!value.includes('SYSDATE') &&
									!value.startsWith('(SELECT')
								) {
									formattedValue = `'${value}'`;
								} else {
									formattedValue = String(value);
								}
								logQuery = logQuery.replace(`:${idx + 1}`, formattedValue);
							});
							console.log(`⚡ QUERY for item ${itemIndex}: 🔄️`, logQuery);

							// Executa a query para o item atual
							const result = await connection.execute(cleanQuery, values || [], {
								autoCommit: false,
							});

							// Processa os resultados do SELECT
							if (result.rows && result.metaData) {
								const columnNames = result.metaData.map((col: { name: string }) => col.name);
								const rowData = result.rows.map((row: any[]) => {
									const mappedRow: IDataObject = {};
									columnNames.forEach((colName: string, idx: number) => {
										mappedRow[colName] = row[idx];
									});
									return mappedRow;
								});

								// Se não encontrou resultados para este item
								if (rowData.length === 0) {
									returnData.push({
										json: { not_found: true },
										pairedItem: items[itemIndex].pairedItem || { item: itemIndex },
										binary: items[itemIndex].binary,
									});
								} else {
									// Para cada resultado encontrado para este item
									// Controla para não exceder o limite global
									for (let rowIndex = 0; rowIndex < rowData.length; rowIndex++) {
										// Verifica se já atingimos o limite global
										if (countResults >= limitValue) {
											break;
										}

										const data = rowData[rowIndex];
										// Processa os dados respeitando a configuração Include Other Fields
										const processedData = processItemData.call(
											this,
											items[itemIndex],
											itemIndex,
											data,
										);

										// Adiciona o resultado mantendo a estrutura do item original
										returnData.push({
											json: processedData,
											pairedItem: items[itemIndex].pairedItem || { item: itemIndex },
											binary: items[itemIndex].binary,
										});

										// Incrementa o contador de resultados
										countResults++;
									}
								}
							}
						}
					} else {
						// O restante do código permanece o mesmo para outros tipos de operação
						// Remove duplicatas de queries para outros modos de operação
						const uniqueQueries = queries.filter(
							(query, index, self) => index === self.findIndex((q) => q.query === query.query),
						);

						for (const [index, { query, values }] of uniqueQueries.entries()) {
							// Remove ponto e vírgula e espaços extras
							const cleanQuery = query.trim().replace(/;$/, '');

							// Atualiza a variável logQuery
							logQuery = cleanQuery;
							values.forEach((value, idx) => {
								let formattedValue: string;
								if (
									typeof value === 'string' &&
									!value.startsWith('TO_DATE') &&
									!value.includes('SYSDATE') &&
									!value.startsWith('(SELECT')
								) {
									formattedValue = `'${value}'`;
								} else {
									formattedValue = String(value);
								}
								logQuery = logQuery.replace(`:${idx + 1}`, formattedValue);
							});
							console.log('⚡ QUERY: 🔄️', logQuery);

							if (
								cleanQuery.trim().toUpperCase().startsWith('DECLARE') ||
								cleanQuery.trim().toUpperCase().startsWith('BEGIN')
							) {
								// Código para blocos PL/SQL permanece o mesmo
								let formattedQuery = cleanQuery.trim();
								if (!formattedQuery.endsWith(';')) {
									formattedQuery += ';';
								}

								await connection.execute(`BEGIN DBMS_OUTPUT.ENABLE(NULL); END;`);
								const result = await connection.execute(formattedQuery, values || [], {
									autoCommit: false,
								});

								const outputMessages = await getDbmsOutput(connection);
								const combinedOutput = outputMessages.join(' ');

								if (
									combinedOutput.includes('Erro:') ||
									combinedOutput.includes('Exception') ||
									combinedOutput.includes('User-Defined Exception')
								) {
									await connection.rollback();
									throw new NodeOperationError(this.getNode(), combinedOutput, {
										description: combinedOutput,
										itemIndex: index,
									});
								}

								// Extrai ROWIDs e informações sobre a operação das mensagens de saída
								let extractedRowids: string[] = [];
								let operationType = 'Executed';

								// Processa cada mensagem de saída para extrair ROWIDs e outras informações
								for (const message of outputMessages) {
									// Procura por padrões como "ROWID: AAAE5WAAEAAAAP3AAA"
									const rowidMatch = message.match(/ROWID:\s*([A-Za-z0-9+\/]+)/i);
									if (rowidMatch && rowidMatch[1]) {
										extractedRowids.push(rowidMatch[1]);
									}

									// Procura por padrões como "ROWID #1: AAAE5WAAEAAAAP3AAA"
									const rowidNumberMatch = message.match(/ROWID\s*#\d+\s*:\s*([A-Za-z0-9+\/]+)/i);
									if (rowidNumberMatch && rowidNumberMatch[1]) {
										extractedRowids.push(rowidNumberMatch[1]);
									}

									// Procura por informações sobre o tipo de operação
									if (message.includes('INSERT') || message.includes('inserted')) {
										operationType = 'Inserted';
									} else if (message.includes('UPDATE') || message.includes('updated')) {
										operationType = 'Updated';
									} else if (message.includes('DELETE') || message.includes('deleted')) {
										operationType = 'Deleted';
									}
								}

								// Se não encontrou ROWIDs nas mensagens, tenta usar o lastRowid
								if (extractedRowids.length === 0 && result.lastRowid) {
									extractedRowids.push(result.lastRowid);
								}

								// Cria uma mensagem formatada para o resultado base
								const baseMessage = `✅ ${operationType}`;

								// Se temos ROWIDs, retornamos um item para cada ROWID
								if (extractedRowids.length > 0) {
									// Para cada ROWID, criamos um item separado
									extractedRowids.forEach((rowid, rowidIndex) => {
										// Cria uma mensagem formatada para este ROWID específico
										const formattedMessage = `${baseMessage} (${rowidIndex + 1} de ${extractedRowids.length})`;

										const successData = {
											seq: rowidIndex + 1,
											rowid: rowid,
											message: formattedMessage,
											success: true,
										};

										// Usamos o primeiro item para manter a conexão com os nós anteriores
										const itemToUse = items[0];
										const processedData = processItemData.call(this, itemToUse, 0, successData);

										returnData.push({
											json: processedData,
											pairedItem: itemToUse?.pairedItem || { item: 0 },
											binary: itemToUse?.binary,
										});
									});
								} else {
									// Se não temos ROWIDs, retornamos um único item
									items.forEach((item, itemIndex) => {
										// Cria uma mensagem formatada para o caso sem ROWIDs
										const noRowidMessage = `${baseMessage} (sem ROWIDs)`;

										const successData = {
											seq: itemIndex + 1,
											rowid: 'N/A',
											message: noRowidMessage,
											success: true,
										};

										generatePairedItemData(itemIndex);
										const processedData = processItemData.call(this, item, itemIndex, successData);

										returnData.push({
											json: processedData,
											pairedItem: item?.pairedItem || { item: itemIndex },
											binary: item?.binary,
										});
									});
								}
							} else if (cleanQuery.trim().toUpperCase().startsWith('DELETE')) {
								// Código para DELETE permanece o mesmo
								const tableName = cleanQuery.split('FROM')[1].split('WHERE')[0].trim();
								const whereClause = cleanQuery.split('WHERE')[1]?.trim();

								const rowidQuery = `SELECT ROWID FROM ${tableName}${whereClause ? ` WHERE ${whereClause}` : ''}`;
								const rowidResult = await connection.execute(rowidQuery, values || [], {
									autoCommit: false,
									prefetchRows: 1000,
									fetchArraySize: 1000,
								});

								const result = await connection.execute(cleanQuery, values || [], {
									autoCommit: false,
								});

								if (result && result.rowsAffected > 0 && rowidResult && rowidResult.rows) {
									items.forEach((item, itemIndex) => {
										rowidResult.rows.forEach((row: any[], idx: number) => {
											const successData = {
												seq: idx + 1,
												rowid: row[0],
												message: `✅ Deleted (${idx + 1} de ${result.rowsAffected})`,
												success: true,
												rows: result.rowsAffected,
											};

											generatePairedItemData(itemIndex);
											const processedData = processItemData.call(
												this,
												item,
												itemIndex,
												successData,
											);

											returnData.push({
												json: processedData,
												pairedItem: item?.pairedItem || { item: itemIndex },
												binary: item?.binary,
											});
										});
									});
								}
							} else {
								// Executa a query com os valores como binding parameters
								const result = await connection.execute(cleanQuery, values || [], {
									autoCommit: false,
								});

								if (result) {
									// Para operações DML (INSERT/UPDATE/DELETE/MERGE)
									if (
										result.rowsAffected > 0 &&
										!cleanQuery.trim().toUpperCase().startsWith('SELECT')
									) {
										const upperQuery = cleanQuery.trim().toUpperCase();
										let tableName = '';
										let whereClause: string | undefined;

										if (upperQuery.startsWith('DELETE')) {
											tableName = cleanQuery.split('FROM')[1].split('WHERE')[0].trim();
											whereClause = cleanQuery.split('WHERE')[1]?.trim();
										} else if (upperQuery.startsWith('UPDATE')) {
											tableName = cleanQuery.split('UPDATE')[1].split('SET')[0].trim();
											whereClause = cleanQuery.split('WHERE')[1]?.trim();
										} else if (upperQuery.startsWith('INSERT')) {
											tableName = cleanQuery.split('INTO')[1].split('(')[0].trim();
											whereClause = `ROWID = '${result.lastRowid}'`;
										}

										if (!tableName) {
											throw new Error('Could not determine table name from query');
										}

										// Define o tipo de operação com base no resultado
										const operationType = determineOperationType(cleanQuery, result);

										// Para operações DML
										items.forEach(async (item, itemIndex) => {
											const operationResults = await processDmlOperation(
												connection,
												result,
												tableName,
												whereClause,
												values,
												operationType,
												itemIndex,
												items,
												this,
											);
											returnData.push(...operationResults);
										});
									}
									// Para SELECT na operação executeQuery
									else if (result.rows && result.metaData) {
										const columnNames = result.metaData.map((col: { name: string }) => col.name);
										const rowData = result.rows.map((row: any[]) => {
											const mappedRow: IDataObject = {};
											columnNames.forEach((colName: string, idx: number) => {
												mappedRow[colName] = row[idx];
											});
											return mappedRow;
										});

										// Se for um SELECT sem resultados
										if (rowData.length === 0) {
											// Mantém a cadeia completa de pairedItem para cada item
											items.forEach((item, itemIndex) => {
												returnData.push({
													json: { not_found: true },
													pairedItem: item.pairedItem || { item: itemIndex },
													binary: item.binary,
												});
											});
										} else {
											// Para o modo executeQuery: adiciona os resultados
											const isExecuteQueryOperation =
												this.getNodeParameter('operation', 0) === 'executeQuery';

											if (isExecuteQueryOperation) {
												// Obtém limit para executeQuery, se existir
												let execQueryLimitValue = Infinity;
												try {
													const limitParam = this.getNodeParameter(
														'options.limit',
														0,
														{},
													) as IDataObject;
													if (limitParam && typeof limitParam === 'number') {
														execQueryLimitValue = limitParam;
													} else if (limitParam && typeof limitParam === 'string') {
														execQueryLimitValue = parseInt(limitParam as string, 10);
													} else if (options.limit) {
														execQueryLimitValue =
															typeof options.limit === 'number'
																? options.limit
																: parseInt(options.limit as string, 10);
													}
												} catch (e) {
													execQueryLimitValue = Infinity;
												}

												// Aplica o limit ao resultado do executeQuery
												const limitedRowData = isNaN(execQueryLimitValue)
													? rowData
													: rowData.slice(0, execQueryLimitValue);

												// Para executeQuery: adiciona cada linha do resultado preservando pairedItem
												limitedRowData.forEach((data: IDataObject, rowIndex: number) => {
													// Processa os dados respeitando a configuração Include Other Fields
													const processedData = processItemData.call(this, items[0], 0, data);

													returnData.push({
														json: processedData,
														pairedItem: items[0]?.pairedItem || { item: 0 },
														binary: items[0]?.binary,
													});
												});
											}
										}
									}
								}
							}
						}
					}
					await connection.commit();
					console.log('✅ Commit realizado com sucesso - modo SINGLE');
				} catch (err) {
					await connection.rollback();
					console.log('⚠️ Rollback executado - modo SINGLE');

					// Recupera as mensagens do DBMS_OUTPUT
					const outputMessages = await getDbmsOutput(connection);
					const dbmsOutput = outputMessages.length ? outputMessages.join('\n') : '';

					// Prepara a mensagem de erro completa
					const errorMessage = dbmsOutput
						? `${err.message}<br>DBMS Output:<br>${dbmsOutput}`
						: err.message;

					// Cria o erro para forçar o fluxo para o Error Branch
					const error = new NodeOperationError(this.getNode(), errorMessage, {
						description: `⚠️ Query:<br>${logQuery}`,
						itemIndex: 0,
					});

					// Força o erro para o Error Branch
					if (!this.continueOnFail()) {
						throw error;
					}

					// Retorna os dados de erro formatados preservando pairedItem
					return [
						{
							json: {
								error: errorMessage,
								query: logQuery,
								success: false,
							},
							error,
							pairedItem: items[0]?.pairedItem || { item: 0 },
							binary: items[0]?.binary,
						},
					];
				}
			} else if (mode === BATCH_MODE.INDEPENDENTLY) {
				// Implementação para o modo INDEPENDENTLY - sem alterações
				for (const [index, queryWithValues] of queries.entries()) {
					try {
						const { query, values } = queryWithValues;
						const result = await connection.execute(query, values || [], { autoCommit: false });

						if (result && result.rowsAffected > 0) {
							generatePairedItemData(index);
							returnData.push({
								json: { success: true, rowsAffected: result.rowsAffected },
								pairedItem: items[index]?.pairedItem || { item: index },
								binary: items[index]?.binary,
							});
						}

						await connection.commit();
					} catch (err) {
						await connection.rollback();
						console.log(`Rollback executado - query ${index}`);
						throw err;
					}
				}
			} else if (mode === BATCH_MODE.TRANSACTION) {
				// Implementação para o modo TRANSACTION - sem alterações
				try {
					for (const [index, queryWithValues] of queries.entries()) {
						const { query, values } = queryWithValues;
						const result = await connection.execute(query, values || [], { autoCommit: false });

						if (result && result.rowsAffected > 0) {
							generatePairedItemData(index);
							returnData.push({
								json: { success: true, rowsAffected: result.rowsAffected },
								pairedItem: items[index]?.pairedItem || { item: index },
								binary: items[index]?.binary,
							});
						}
					}
					await connection.commit();
				} catch (err) {
					await connection.rollback();
					console.log('Rollback executado - modo TRANSACTION');
					throw err;
				}
			}
		} catch (err) {
			console.error('Erro geral ao configurar query runner:', err);
			throw err;
		} finally {
			if (connection) {
				try {
					console.log('❌ Fechando conexão...⌛');
					await connection.close();
				} catch (closeError) {
					console.error('Erro ao fechar conexão:', closeError);
				}
			}
		}

		return returnData;
	};
}
// Função para obter a conexão compartilhada
export function getSharedConnection(): any {
	return sharedConnection;
}

// Função para fechar a conexão compartilhada
export async function closeSharedConnection(): Promise<void> {
	if (sharedConnection && !sharedConnection.isClosed) {
		try {
			await sharedConnection.close();
			sharedConnection = null;
		} catch (error) {
			console.error('Erro ao fechar conexão compartilhada:', error);
		}
	}
}

export const addWhereClauses = (
	p0: INode,
	i: number,
	query: string,
	where: WhereClause[],
	initialValues: QueryValues,
	combineWith = 'AND',
): [string, QueryValues] => {
	const values: QueryValues = [...initialValues];

	if (!where?.length) {
		return [query, values];
	}

	const conditions = where.map((condition) => {
		if (['IS NULL', 'IS NOT NULL'].includes(condition.condition)) {
			return `${escapeSqlIdentifier(condition.column)} ${condition.condition}`;
		}

		const operator = condition.condition === 'equal' ? '=' : condition.condition;
		values.push(condition.value);
		return `${escapeSqlIdentifier(condition.column)} ${operator} :${values.length}`;
	});

	return [`${query} WHERE ${conditions.join(` ${combineWith} `)}`, values];
};

export function addSortRules(
	query: string,
	rules: SortRule[],
	replacements: QueryValues,
): [string, QueryValues] {
	if (rules.length === 0) return [query, replacements];

	let orderByQuery = ' ORDER BY';
	const values: string[] = [];

	rules.forEach((rule, index) => {
		const endWith = index === rules.length - 1 ? '' : ',';

		orderByQuery += ` ${escapeSqlIdentifier(rule.column)} ${rule.direction}${endWith}`;
	});

	return [`${query}${orderByQuery}`, replacements.concat(...values)];
}

export function replaceEmptyStringsByNulls(
	items: INodeExecutionData[],
	replace?: boolean,
): INodeExecutionData[] {
	if (!replace) return [...items];

	const returnData: INodeExecutionData[] = items.map((item) => {
		const newItem = { ...item };
		const keys = Object.keys(newItem.json);

		for (const key of keys) {
			if (newItem.json[key] === '') {
				newItem.json[key] = null;
			}
		}
		return newItem;
	});

	return returnData;
}

export function specialValues(
	value: any,
	metadata?: {
		maxLength?: number;
		dataType?: string;
	},
): { isSpecial: boolean; value: string } {
	// Valores nulos
	if (value === null || value === undefined) {
		return { isSpecial: true, value: 'NULL' };
	}

	// Tratamento por tipo
	const dataType = metadata?.dataType?.toUpperCase();
	let sanitizedValue;

	switch (dataType) {
		case 'VARCHAR2':
		case 'CHAR':
		case 'NVARCHAR2':
		case 'NCHAR':
			// Sanitiza o valor (remove espaços, converte para maiúsculas, etc)
			sanitizedValue = sanitizeValue(value, metadata);
			if (sanitizedValue === null) {
				return { isSpecial: true, value: 'NULL' };
			}
			return { isSpecial: false, value: `'${sanitizedValue}'` };

		case 'DATE':
		case 'TIMESTAMP':
			if (typeof value === 'string') {
				const trimmedValue = value.trim();
				if (trimmedValue.toUpperCase().includes('SYSDATE')) {
					return { isSpecial: true, value: 'SYSDATE' };
				}
				if (trimmedValue.toUpperCase().includes('TO_DATE')) {
					return { isSpecial: true, value: trimmedValue };
				}
				// Detecta o formato correto baseado no valor
				let format;
				if (trimmedValue.match(/^\d{2}\/\d{2}\/\d{4}\s\d{2}:\d{2}:\d{2}$/)) {
					format = 'DD/MM/YYYY HH24:MI:SS';
				} else if (trimmedValue.match(/^\d{2}\/\d{2}\/\d{4}\s\d{2}:\d{2}$/)) {
					format = 'DD/MM/YYYY HH24:MI';
				} else if (trimmedValue.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
					format = 'DD/MM/YYYY';
				} else {
					format = 'DD/MM/YYYY HH24:MI:SS';
				}
				return {
					isSpecial: true,
					value: `TO_DATE('${trimmedValue}', '${format}')`,
				};
			}
			return { isSpecial: true, value: 'SYSDATE' };

		case 'NUMBER':
		case 'INTEGER':
		case 'FLOAT':
		case 'DECIMAL':
			const numValue = Number(value);
			if (isNaN(numValue)) {
				console.warn(`⚠️ Valor não numérico convertido para 0: ${value}`);
				return { isSpecial: false, value: '0' };
			}
			return { isSpecial: false, value: numValue.toString() };

		default:
			if (typeof value === 'string') {
				// Sanitiza o valor (remove espaços, converte para maiúsculas, etc)
				sanitizedValue = sanitizeValue(value, metadata);
				if (sanitizedValue === null) {
					return { isSpecial: true, value: 'NULL' };
				}
				return { isSpecial: false, value: `'${sanitizedValue}'` };
			}
			if (typeof value === 'number') return { isSpecial: false, value: value.toString() };
			if (typeof value === 'boolean') return { isSpecial: false, value: value ? '1' : '0' };

			// Para outros tipos, sanitiza o valor
			sanitizedValue = sanitizeValue(value, metadata);
			if (sanitizedValue === null) {
				return { isSpecial: true, value: 'NULL' };
			}
			return { isSpecial: false, value: `'${sanitizedValue}'` };
	}
}

// Função auxiliar para processar o resultado de operações DML
async function processDmlOperation(
	connection: any,
	result: any,
	tableName: string,
	whereClause: string | undefined,
	values: any[],
	operationType: string,
	index: number,
	items: INodeExecutionData[],
	context: IExecuteFunctions,
): Promise<INodeExecutionData[]> {
	const returnData: INodeExecutionData[] = [];

	if (result && result.rowsAffected > 0) {
		// Cria um único resultado para a operação
		const successData = {
			seq: index + 1,
			rowid: result.lastRowid || 'N/A',
			message: `✅ ${operationType} (${result.rowsAffected} registro(s))`,
			success: true,
			rows: result.rowsAffected,
		};

		// Processa apenas uma vez
		const processedData = processItemData.call(
			context,
			items[index] || { json: {} },
			index,
			successData,
		);

		returnData.push({
			json: processedData,
			pairedItem: items[index]?.pairedItem || { item: index },
			binary: items[index]?.binary,
		});
	}

	return returnData;
}

// Função auxiliar para determinar o tipo real da operação
function determineOperationType(query: string, result: any): string {
	const upperQuery = query.trim().toUpperCase();

	if (upperQuery.startsWith('DECLARE') || upperQuery.startsWith('BEGIN')) {
		return 'Executed';
	}

	// Para MERGE/UPSERT, verifica se houve INSERT ou UPDATE
	if (upperQuery.startsWith('MERGE')) {
		return result.lastRowid ? 'Inserted' : 'Updated';
	}

	if (upperQuery.startsWith('INSERT')) return 'Inserted';
	if (upperQuery.startsWith('UPDATE')) return 'Updated';
	if (upperQuery.startsWith('DELETE')) return 'Deleted';

	return 'Executed';
}

async function getDbmsOutput(connection: any): Promise<string[]> {
	const outputMessages: string[] = [];
	let result;

	do {
		result = await connection.execute(`BEGIN DBMS_OUTPUT.GET_LINE(:ln, :st); END;`, {
			ln: { dir: oracledb.BIND_OUT, type: oracledb.STRING, maxSize: 32767 },
			st: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
		});
		if (result.outBinds.st === 0 && result.outBinds.ln) {
			outputMessages.push(result.outBinds.ln);
		}
	} while (result.outBinds.st === 0);

	return outputMessages;
}

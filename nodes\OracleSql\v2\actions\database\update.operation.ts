import {
	type IDataObject,
	type IExecuteFunctions,
	type INodeExecutionData,
	type INodeProperties,
} from 'n8n-workflow';

import type { QueryRunner, QueryWithValues } from '../../helpers/interfaces';
import { DATA_MODE, MANUAL, WhereClause } from '../../helpers/interfaces';

import {
	escapeSqlIdentifier,
	replaceEmptyStringsByNulls,
	specialValues,
} from '../../helpers/utils';

import { updateDisplayOptions } from '../../../../../utils/utilities';
import { optionsCollection, selectRowsFixedCollection } from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Data Mode',
		name: 'dataMode',
		type: 'options',
		options: [
			{
				name: 'Auto-Map Input Data to Columns',
				value: DATA_MODE.AUTO_MAP,
				description: 'Use when node input properties names exactly match the table column names',
			},
			{
				name: 'Map Each Column Manually',
				value: DATA_MODE.MANUAL,
				description: 'Set the value for each destination column manually',
			},
		],
		default: MANUAL,
		description:
			'Whether to map node input properties and the table data automatically or manually',
	},
	{
		displayName: `
		In this mode, make sure incoming data fields are named the same as the columns in your table. If needed, use an 'Edit Fields' node before this node to change the field names.
		`,
		name: 'notice',
		type: 'notice',
		default: '',
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.AUTO_MAP],
			},
		},
	},
	{
		displayName: 'Values to Send',
		name: 'fields', // Mudando de 'valuesToSend' para 'fields'
		placeholder: 'Add Field',
		type: 'fixedCollection',
		typeOptions: {
			multipleValueButtonText: 'Add Field',
			multipleValues: true,
		},
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
		default: {},
		options: [
			{
				displayName: 'Fields',
				name: 'string',
				values: [
					{
						displayName: 'Field',
						name: 'name',
						type: 'options',
						typeOptions: {
							loadOptionsMethod: 'getColumns',
							loadOptionsDependsOn: ['table.value'],
						},
						default: [],
					},
					{
						displayName: 'Value',
						name: 'value',
						type: 'string',
						default: '',
					},
				],
			},
		],
	},
	{
		...selectRowsFixedCollection,
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
	},
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['update'],
	},
	hide: {
		table: [''],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
	nodeOptions: IDataObject,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);
	const queries: QueryWithValues[] = [];

	for (let i = 0; i < items.length; i++) {
		const table = this.getNodeParameter('table', i, undefined, {
			extractValue: true,
		}) as string;

		const dataMode = this.getNodeParameter('dataMode', i) as string;
		let item: IDataObject = {};

		if (dataMode === DATA_MODE.AUTO_MAP) {
			item = items[i].json;
		}

		if (dataMode === DATA_MODE.MANUAL) {
			// Obtém o parâmetro fields com valor padrão seguro
			const fieldsParameter = this.getNodeParameter('fields', i, { string: [] }) as IDataObject;
			const fields = (fieldsParameter?.string || []) as IDataObject[];

			// Usa o reduce com validação de propriedades
			item = Array.isArray(fields)
				? fields.reduce((acc, field) => {
						if (field && typeof field === 'object' && 'name' in field && 'value' in field) {
							acc[field.name as string] = field.value;
						}
						return acc;
					}, {} as IDataObject)
				: {};
		}

		const updateColumns = Object.keys(item);
		const updates: string[] = [];

		// Processa os campos para UPDATE
		for (const column of updateColumns) {
			const value = item[column];

			if (value === null || value === undefined) {
				updates.push(`${escapeSqlIdentifier(column)} = NULL`);
			} else if (
				value instanceof Date ||
				Object.prototype.toString.call(value) === '[object Date]'
			) {
				const dateValue = value as Date;
				updates.push(
					`${escapeSqlIdentifier(column)} = TO_DATE('${dateValue.toLocaleString('pt-BR')}', 'DD/MM/YYYY HH24:MI:SS')`,
				);
			} else if (typeof value === 'string') {
				// Verifica se é uma data em formato string
				const datePattern = /^\d{2}\/\d{2}\/\d{4}( \d{2}:\d{2}:\d{2})?$/;
				if (datePattern.test(value)) {
					updates.push(
						`${escapeSqlIdentifier(column)} = TO_DATE('${value}', 'DD/MM/YYYY HH24:MI:SS')`,
					);
				} else if (value.toUpperCase() === 'SYSDATE') {
					updates.push(`${escapeSqlIdentifier(column)} = SYSDATE`);
				} else if (value.trim().toUpperCase().startsWith('(SELECT')) {
					updates.push(`${escapeSqlIdentifier(column)} = ${value}`);
				} else {
					updates.push(`${escapeSqlIdentifier(column)} = '${value.replace(/'/g, "''")}'`);
				}
			} else if (typeof value === 'boolean') {
				updates.push(`${escapeSqlIdentifier(column)} = ${value ? 1 : 0}`);
			} else {
				updates.push(`${escapeSqlIdentifier(column)} = ${value}`);
			}
		}

		// Processa as condições WHERE sem placeholders
		const whereClauses =
			((this.getNodeParameter('where', i, []) as IDataObject).values as WhereClause[]) || [];
		let whereClause = '';

		if (whereClauses.length > 0) {
			const whereConditions = whereClauses.map((clause) => {
				const { column, condition, value } = clause;

				// Converte o operador 'equal' para '='
				const operator = condition === 'equal' ? '=' : condition;

				if (operator === 'IS NULL' || operator === 'IS NOT NULL') {
					return `${escapeSqlIdentifier(column)} ${operator}`;
				}

				if (value === null || value === undefined) {
					return `${escapeSqlIdentifier(column)} IS NULL`;
				}

				const special = specialValues(value as string);
				let finalValue: string;

				if (special.isSpecial) {
					finalValue = special.value;
				} else {
					finalValue =
						typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value.toString();
				}

				return `${escapeSqlIdentifier(column)} ${operator} ${finalValue}`;
			});

			whereClause = `WHERE ${whereConditions.join(' AND ')}`;
		} else {
			whereClause = 'WHERE 1=1';
		}

		const query = `UPDATE ${escapeSqlIdentifier(table)} SET ${updates.join(', ')} ${whereClause}`;

		queries.push({
			query,
			values: [], // Sem placeholders, não precisamos de valores
		});
	}

	returnData = await runQueries(queries);
	return returnData;
}

# Oracle Instant Client Setup Guide

Este guia explica como configurar o Oracle Instant Client para usar o Oracle Trigger com CQN.

## 🚀 Instalação Automática (Recomendada)

Execute o comando para instalação completa:

```bash
npm run setup
```

Ou apenas para instalar o Oracle Instant Client:

```bash
npm run install-instantclient
```

## 📁 Estrutura de Arquivos

Após a instalação, você terá a seguinte estrutura:

```
nodes/OracleSql/v2/lib/
├── instantclient/          # Oracle Instant Client files
│   ├── oci.dll
│   ├── oraociei21.dll
│   ├── oraocci21.dll
│   └── ... (outros arquivos)
├── tns/                    # TNS configuration (auto-generated)
│   ├── tnsnames.ora
│   └── sqlnet.ora
└── README.md
```

## 🔧 Instalação Manual

Se a instalação automática não funcionar:

### 1. Download Manual

1. Acesse: https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html
2. Baixe o **Oracle Instant Client Basic** (versão 19c ou superior)
3. Extraia para: `nodes/OracleSql/v2/lib/instantclient/`

### 2. Verificação

Certifique-se de que os seguintes arquivos estão presentes:

- `nodes/OracleSql/v2/lib/instantclient/oci.dll`
- `nodes/OracleSql/v2/lib/instantclient/oraociei21.dll`
- `nodes/OracleSql/v2/lib/instantclient/oraocci21.dll`

## ⚙️ Configuração Automática

O Oracle Trigger irá automaticamente:

1. **Detectar o Oracle Instant Client** na pasta `instantclient/`
2. **Criar arquivos TNS dinâmicos** baseados nas credenciais:
   - `tnsnames.ora` - Configuração de conexão
   - `sqlnet.ora` - Configurações de rede
3. **Configurar variáveis de ambiente** necessárias
4. **Inicializar o modo thick** para suporte a CQN

## 🔍 Troubleshooting

### Erro DPI-1047 (Architecture Mismatch)

```
DPI-1047: Cannot locate a 64-bit Oracle Client library
```

**Solução:**
- Certifique-se de baixar a versão **x64** do Instant Client
- Verifique se não há versões 32-bit no PATH do sistema
- Use o script automático: `npm run install-instantclient`

### Erro NJS-530 (Network Error)

```
NJS-530: The host addresses or URLs provided by the connect string are incorrect
```

**Solução:**
- Verifique se as credenciais estão corretas
- Teste a conectividade com o banco Oracle
- Verifique se o serviço Oracle está rodando
- Confirme o formato da connectString: `host:port/service`

### CQN não funciona

**Verificações:**
1. **Privilégios do usuário:**
   ```sql
   GRANT CHANGE NOTIFICATION TO seu_usuario;
   ```

2. **Job Queue habilitado:**
   ```sql
   SELECT NAME, VALUE FROM V$PARAMETER WHERE NAME = 'job_queue_processes';
   -- Deve ser > 0
   ```

3. **Modo thick ativo:**
   - Verifique se o Oracle Instant Client está instalado
   - Configure o modo "thick" nas credenciais

### Oracle Instant Client não detectado

**Verificações:**
1. Arquivo `oci.dll` existe em `nodes/OracleSql/v2/lib/instantclient/`
2. Permissões de leitura na pasta
3. Arquitetura correta (x64)

## 🎯 Configuração do Oracle Trigger

### 1. Credenciais

- **Driver Mode**: Selecione "thick"
- **Connect String**: `host:port/service` (ex: `localhost:1521/XE`)
- **User/Password**: Usuário com privilégios CQN

### 2. Configuração do Trigger

- **Table**: Selecione a tabela para monitorar
- **Column**: Escolha a coluna ou use "ROWID" para qualquer alteração
- **Operation Type**: INSERT, UPDATE, DELETE ou All Operations
- **Where Conditions**: Filtros opcionais

### 3. Teste

1. Configure o trigger
2. Execute uma operação na tabela (INSERT/UPDATE/DELETE)
3. Verifique se a notificação é recebida

## 📋 Logs Úteis

O Oracle Trigger exibe logs detalhados:

```
✅ Oracle Instant Client encontrado em: C:\path\to\instantclient
🔄 Inicializando Oracle Client no modo thick
📝 tnsnames.ora criado em: C:\path\to\tns\tnsnames.ora
🔗 Usando connectString para CQN: ORACLE_CQN
✅ Oracle Client inicializado com sucesso no modo thick
```

## 📄 Licença

O Oracle Instant Client está sujeito à licença da Oracle:
https://www.oracle.com/downloads/licenses/instant-client-lic.html

## 🆘 Suporte

Se encontrar problemas:

1. Verifique os logs do n8n
2. Confirme que o Oracle Instant Client está instalado corretamente
3. Teste a conectividade com o banco Oracle
4. Verifique os privilégios do usuário no banco

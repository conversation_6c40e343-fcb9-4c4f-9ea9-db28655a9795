import type { ICredentialType, INodeProperties } from 'n8n-workflow';
import { sshTunnelProperties } from '../utils/sshTunnel.properties';
export class OracleSql implements ICredentialType {
	name = 'oracleSql';

	displayName = 'OracleSQL API';

	// eslint-disable-next-line n8n-nodes-base/cred-class-field-documentation-url-not-http-url
	documentationUrl = 'oracleSql';
	icon = { light: 'file:oracle.svg', dark: 'file:oracle.dark.svg' } as const;

	properties: INodeProperties[] = [
		{
			displayName: 'Host',
			name: 'host',
			type: 'string',
			default: '*************',
			description: 'Host do servidor Oracle',
		},
		{
			displayName: 'Service Name / SID',
			name: 'sid',
			type: 'string',
			default: 'TESTE',
			description: 'Nome do serviço ou SID do banco Oracle',
		},
		{
			displayName: 'User',
			name: 'user',
			type: 'string',
			default: 'TESTE',
			description: 'Nome de usuário para conexão',
		},
		{
			displayName: 'Password',
			name: 'password',
			type: 'string',
			typeOptions: {
				password: true,
			},
			default: '',
			description: 'Senha para conexão',
		},
		{
			displayName: 'Port',
			name: 'port',
			type: 'number',
			default: 1521,
			description: 'Porta do servidor Oracle',
		},
		{
			displayName: 'Connection String',
			name: 'connectString',
			type: 'hidden',
			default:
				'={{$parameter["host"] + ":" + (parseInt($parameter["port"]) || 1521) + "/" + $parameter["sid"]}}',
			description: 'String de conexão gerada automaticamente no formato Easy Connect',
			noDataExpression: false,
		},
		{
			displayName: 'Connect Timeout',
			name: 'connectTimeout',
			type: 'number',
			default: 10000,
			description:
				'The milliseconds before a timeout occurs during the initial connection to the OracleSQL server',
		},
		{
			displayName: 'SSL',
			name: 'ssl',
			type: 'boolean',
			default: false,
		},
		{
			displayName: 'Driver Mode',
			name: 'driverMode',
			type: 'options',
			options: [
				{
					name: 'Thin (Default)',
					value: 'thin',
					description: 'Pure JavaScript implementation, supports Oracle 12.1+',
				},
				{
					name: 'Thick',
					value: 'thick',
					description:
						'Uses Oracle Client libraries, supports all Oracle versions and advanced features',
				},
			],
			default: 'thin',
			description: 'Oracle driver mode to use for connection',
		},
		...sshTunnelProperties,
	];
}

import type { IDataObject, ILoadOptionsFunctions, INodeListSearchResult } from 'n8n-workflow';
import type { OracleSqlNodeCredentials } from '../helpers/interfaces';
import { oracleDriverManager } from '../helpers/oracleDriverManager';
import { createPool } from '../transport';

// Get the Oracle driver from the manager
const oracledb = oracleDriverManager.getDriver();

export async function searchTables(
	this: ILoadOptionsFunctions,
	filter?: string,
): Promise<INodeListSearchResult> {
	const credentials = (await this.getCredentials('oracleSql')) as OracleSqlNodeCredentials;
	if (!credentials) {
		throw new Error('No credentials found!');
	}

	const schema = credentials.user;

	const pool = await createPool.call(this, credentials);
	const connection = await pool.getConnection();

	try {
		const searchTerm = (filter || '').toUpperCase();

		// Ajustando a consulta SQL para dar prioridade à tabela que corresponde exatamente ao filtro
		const query = `
      SELECT TABLE_NAME
        FROM (
          SELECT TABLE_NAME
            FROM ALL_TABLES
           WHERE OWNER = :schema
             AND TABLE_NAME LIKE :searchTerm
           ORDER BY
             CASE
               WHEN TABLE_NAME = :searchTerm THEN 0
               ELSE 1
             END,
             TABLE_NAME ASC
        )
       WHERE ROWNUM <= 10
    `;

		const result = await connection.execute(
			query,
			{ schema, searchTerm: `${searchTerm}%` },
			{
				outFormat: oracledb.OUT_FORMAT_OBJECT,
			},
		);

		const tables = (result.rows as IDataObject[]).map((table) => ({
			name: (table.TABLE_NAME as string) || '',
			value: (table.TABLE_NAME as string) || '',
		}));

		return {
			results: tables,
		};
	} catch (error) {
		throw new Error(`Error while searching for tables: ${(error as Error).message}`);
	} finally {
		await connection.close();
	}
}

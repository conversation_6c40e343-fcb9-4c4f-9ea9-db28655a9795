import type {
	IDataObject,
	IExecuteFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';
import { NodeOperationError } from 'n8n-workflow';

import type { QueryRunner, QueryWithValues, WhereClause } from '../../helpers/interfaces';

import { escapeSqlIdentifier, specialValues } from '../../helpers/utils';

import { updateDisplayOptions } from '../../../../../utils/utilities';
import {
	combineConditionsCollection,
	optionsCollection,
	selectRowsFixedCollection,
} from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Command',
		name: 'deleteCommand',
		type: 'options',
		default: 'delete',
		options: [
			{
				name: 'Truncate',
				value: 'truncate',
				description: 'Apenas remove os dados da tabela e preserva a estrutura da tabela',
			},
			{
				name: 'Delete',
				value: 'delete',
				description:
					"Exclua as linhas que correspondem às condições 'Selecionar linhas' abaixo.Se nenhuma seleção for feita, todas as linhas da tabela serão excluídas.",
			},
			{
				name: 'Drop',
				value: 'drop',
				description: 'Exclui os dados da tabela e também a estrutura da tabela permanentemente',
			},
		],
	},
	{
		...selectRowsFixedCollection,
		displayOptions: {
			show: {
				deleteCommand: ['delete'],
			},
		},
	},
	{
		...combineConditionsCollection,
		displayOptions: {
			show: {
				deleteCommand: ['delete'],
			},
		},
	},
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['deleteRows'], // Confirmar que está como 'deleteRows'
	},
	hide: {
		table: [''],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const queries: QueryWithValues[] = [];

	for (let i = 0; i < inputItems.length; i++) {
		const table = this.getNodeParameter('table', i, undefined, {
			extractValue: true,
		}) as string;

		const deleteCommand = this.getNodeParameter('deleteCommand', i) as string;
		let query = '';

		if (deleteCommand === 'drop') {
			query = `DROP TABLE IF EXISTS ${escapeSqlIdentifier(table)}`;
		}

		if (deleteCommand === 'truncate') {
			query = `TRUNCATE TABLE ${escapeSqlIdentifier(table)}`;
		}

		if (deleteCommand === 'delete') {
			const whereClauses =
				((this.getNodeParameter('where', i, []) as IDataObject).values as WhereClause[]) || [];
			const combineConditions = this.getNodeParameter('combineConditions', i, 'AND') as string;

			// Processa as cláusulas WHERE com valores diretos
			const conditions = whereClauses.map((clause) => {
				const special = specialValues(clause.value, { dataType: clause.dataType });
				const formattedValue = special.isSpecial ? special.value : `'${clause.value}'`;

				if (['IS NULL', 'IS NOT NULL'].includes(clause.condition)) {
					return `${escapeSqlIdentifier(clause.column)} ${clause.condition}`;
				}

				const operator = clause.condition === 'equal' ? '=' : clause.condition;
				return `${escapeSqlIdentifier(clause.column)} ${operator} ${formattedValue}`;
			});

			// Constrói a query DELETE com os valores diretos
			if (conditions.length > 0) {
				query = `DELETE FROM ${escapeSqlIdentifier(table)} WHERE ${conditions.join(` ${combineConditions} `)}`;
			} else {
				query = `DELETE FROM ${escapeSqlIdentifier(table)}`;
			}

			console.log('⚡ QUERY: 🔄️', query);
		}

		if (query === '') {
			throw new NodeOperationError(
				this.getNode(),
				'Invalid delete command, only drop, delete and truncate are supported',
				{ itemIndex: i },
			);
		}

		// Adiciona a query sem usar placeholders
		queries.push({ query, values: [] });
	}

	returnData = await runQueries(queries);
	return returnData;
}

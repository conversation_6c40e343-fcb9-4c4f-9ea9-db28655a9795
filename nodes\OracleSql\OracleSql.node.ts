import type { INodeTypeBaseDescription, IVersionedNodeType } from 'n8n-workflow';
import { VersionedNodeType } from 'n8n-workflow';

import { OracleSqlV2 } from './v2/OracleSqlV2.node';

export class OracleSql extends VersionedNodeType {
  constructor() {
    const baseDescription: INodeTypeBaseDescription = {
      displayName: 'Oracle SQL',
      name: 'oracleSql',
      icon: { light: 'file:oracle.svg', dark: 'file:oracle.dark.svg' },
      group: ['input'],
      defaultVersion: 2.4,
      description: 'Get, add and update data in Oracle SQL',
      parameterPane: 'wide',
    };

    const nodeVersions: IVersionedNodeType['nodeVersions'] = {
      2: new OracleSqlV2(baseDescription),
      2.1: new OracleSqlV2(baseDescription),
      2.2: new OracleSqlV2(baseDescription),
      2.3: new OracleSqlV2(baseDescription),
      2.4: new OracleSqlV2(baseDescription),
    };

    super(nodeVersions, baseDescription);
  }
}

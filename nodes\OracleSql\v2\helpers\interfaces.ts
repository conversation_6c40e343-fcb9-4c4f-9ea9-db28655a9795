import { EventEmitter } from 'events';
import type { IDataObject, IExecuteFunctions, INodeExecutionData } from 'n8n-workflow';
import OracleDB from 'oracledb';

export type OracleSqlConnection = OracleDB.Connection;
export type OracleSqlPool = OracleDB.Pool;
// export type OracleSqlOkPacket = OracleDB.OkPacket;

export type QueryValues = Array<string | number | IDataObject>;
export type QueryWithValues = { query: string; values: QueryValues };

export type QueryRunner = (queries: QueryWithValues[]) => Promise<INodeExecutionData[]>;

export type ConfigureQueryRunner = (
	this: IExecuteFunctions,
	options: IDataObject,
	pool: OracleSqlPool,
	items: INodeExecutionData[],
) => QueryRunner;

// Driver mode change event name
export const DRIVER_MODE_CHANGE_EVENT = 'driverModeChange';

// Global event emitter for Oracle driver events
export const oracleEvents = new EventEmitter();

export interface WhereClause {
	column: string;
	condition: string;
	value: string | number;
	dataType?: string; // Removida a duplicação
}

export interface ColumnInfo {
	name: string;
	type: string;
	maxLength?: number;
	nullable?: boolean;
	dataType?: string; // Movido para interface correta
}

export type SortRule = { column: string; direction: string };

export const AUTO_MAP = 'autoMapInputData';
export const MANUAL = 'defineBelow';
export const DATA_MODE = {
	AUTO_MAP,
	MANUAL,
};

export const SINGLE = 'single';
const TRANSACTION = 'transaction';
const INDEPENDENTLY = 'independently';
export const BATCH_MODE = { SINGLE, TRANSACTION, INDEPENDENTLY };

export type QueryMode = typeof SINGLE | typeof TRANSACTION | typeof INDEPENDENTLY;

type WithSSL = { ssl: false } | { ssl: true };

export type OracleSqlNodeCredentials = {
	user: string;
	password: string;
	connectString: string;
	host?: string;
	port?: number;
	sid?: string;
	driverMode?: 'thin' | 'thick';
} & WithSSL;

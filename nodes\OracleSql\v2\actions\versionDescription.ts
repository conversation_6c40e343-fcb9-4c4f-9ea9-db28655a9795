/* eslint-disable n8n-nodes-base/node-filename-against-convention */
import { NodeConnectionType, type INodeTypeDescription } from 'n8n-workflow';

import * as database from './database/Database.resource';

export const versionDescription: INodeTypeDescription = {
	displayName: 'Oracle SQL',
	name: 'oracleSql',
	icon: { light: 'file:oracle.svg', dark: 'file:oracle.dark.svg' },
	group: ['input'],
	version: [2, 2.1, 2.2, 2.3, 2.4],
	subtitle: '={{ $parameter["operation"] }}',
	description: 'Get, add and update data in Oracle SQL',
	defaults: {
		name: 'Oracle SQL',
	},
	inputs: [NodeConnectionType.Main],
	outputs: [NodeConnectionType.Main],
	usableAsTool: true,
	credentials: [
		{
			name: 'oracleSql',
			required: true,
			testedBy: 'oracleSqlConnectionTest',
		},
	],
	properties: [
		{
			displayName: 'Resource',
			name: 'resource',
			type: 'hidden',
			noDataExpression: true,
			options: [
				{
					name: 'Database',
					value: 'database',
				},
			],
			default: 'database',
		},
		...database.description,
		{
			displayName: 'Include Other Fields',
			name: 'includeOtherFields',
			type: 'boolean',
			default: false,
			description: 'Whether to include other fields from input',
		},
		{
			displayName: 'Input Fields to Include',
			name: 'include',
			type: 'options',
			description: 'Select fields to include in output',
			default: 'all',
			displayOptions: {
				show: {
					includeOtherFields: [true],
				},
			},
			options: [
				{
					name: 'All',
					value: 'all',
				},
				{
					name: 'Selected',
					value: 'selected',
				},
				{
					name: 'All Except',
					value: 'except',
				},
			],
		},
		{
			displayName: 'Fields to Include',
			name: 'includeFields',
			type: 'string',
			default: '',
			placeholder: 'field1,field2',
			requiresDataPath: 'multiple',
			displayOptions: {
				show: {
					include: ['selected'],
					includeOtherFields: [true],
				},
			},
		},
		{
			displayName: 'Fields to Exclude',
			name: 'excludeFields',
			type: 'string',
			default: '',
			placeholder: 'field1,field2',
			requiresDataPath: 'multiple',
			displayOptions: {
				show: {
					include: ['except'],
					includeOtherFields: [true],
				},
			},
		},
	],
};

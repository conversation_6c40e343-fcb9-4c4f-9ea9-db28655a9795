import type { IExecuteFunctions, INodeExecutionData } from 'n8n-workflow';
import { NodeOperationError } from 'n8n-workflow';

import type { OracleSqlNodeCredentials, QueryRunner } from '../helpers/interfaces';
import { configureQueryRunner } from '../helpers/utils';
import { createPool } from '../transport';
import * as database from './database/Database.resource';
import type { OracleSqlType } from './node.type';

export async function router(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
	let returnData: INodeExecutionData[] = [];

	const resource = this.getNodeParameter<OracleSqlType>('resource', 0);
	const operation = this.getNodeParameter('operation', 0);
	const nodeOptions = this.getNodeParameter('options', 0);
	const items = this.getInputData();

	nodeOptions.nodeVersion = this.getNode().typeVersion;

	const credentials = (await this.getCredentials(
		'oracleSql',
	)) as unknown as OracleSqlNodeCredentials;

	const pool = await createPool.call(this, credentials, nodeOptions);

	try {
		const runQueries: QueryRunner = configureQueryRunner.call(this, nodeOptions, pool, items);

		const oraclesqlNodeData = {
			resource,
			operation,
		} as OracleSqlType;

		switch (oraclesqlNodeData.resource) {
			case 'database':
				returnData = await database[oraclesqlNodeData.operation].execute.call(
					this,
					items,
					runQueries,
					nodeOptions,
				);
				break;
			default:
				throw new NodeOperationError(
					this.getNode(),
					`The operation "${operation}" is not supported!`,
				);
		}
	} finally {
		await pool.close();
	}

	return [returnData];
}

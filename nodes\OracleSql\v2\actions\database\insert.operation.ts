import type {
	IDataObject,
	IExecuteFunctions,
	ILoadOptionsFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';
import * as oracledb from 'oracledb';

import type {
	QueryMode,
	QueryRunner,
	QueryValues,
	QueryWithValues,
} from '../../helpers/interfaces';

import { BATCH_MODE, DATA_MODE, MANUAL } from '../../helpers/interfaces';

import {
	escapeSqlIdentifier,
	replaceEmptyStringsByNulls,
	specialValues,
} from '../../helpers/utils';

import { updateDisplayOptions } from '../../../../../utils/utilities';
import { getColumns } from '../../methods/loadOptions';
import { optionsCollection } from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Data Mode',
		name: 'dataMode',
		type: 'options',
		options: [
			{
				name: 'Auto-Map Input Data to Columns',
				value: DATA_MODE.AUTO_MAP,
				description: 'Use when node input properties names exactly match the table column names',
			},
			{
				name: 'Map Each Column Manually',
				value: DATA_MODE.MANUAL,
				description: 'Set the value for each destination column manually',
			},
		],
		default: MANUAL,
		description:
			'Whether to map node input properties and the table data automatically or manually',
	},
	{
		displayName: `
		In this mode, make sure incoming data fields are named the same as the columns in your table. If needed, use an 'Edit Fields' node before this node to change the field names.
		`,
		name: 'notice',
		type: 'notice',
		default: '',
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.AUTO_MAP],
			},
		},
	},
	{
		displayName: 'Values to Send',
		name: 'fields', // mantém o nome 'fields' em todas operações
		placeholder: 'Add Field',
		type: 'fixedCollection',
		typeOptions: {
			multipleValueButtonText: 'Add Field',
			multipleValues: true,
		},
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
		default: {},
		options: [
			{
				displayName: 'Fields',
				name: 'string', // mantém o name 'string' em todas operações
				values: [
					{
						displayName: 'Field',
						name: 'name',
						type: 'options',
						typeOptions: {
							loadOptionsMethod: 'getColumns',
							loadOptionsDependsOn: ['table.value'],
						},
						default: [],
					},
					{
						displayName: 'Value',
						name: 'value',
						type: 'string',
						default: '',
					},
				],
			},
		],
	},
	{
		...optionsCollection,
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
	},
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['insert'],
	},
	hide: {
		table: [''],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);
export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
	nodeOptions: IDataObject,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);

	const table = this.getNodeParameter('table', 0, '', { extractValue: true }) as string;

	// Fetch column metadata using the existing loadOptions method
	const columnMetadata = await getColumns.call(this as unknown as ILoadOptionsFunctions);

	const dataMode = this.getNodeParameter('dataMode', 0) as string;
	const queryBatching = (nodeOptions.queryBatching as QueryMode) || BATCH_MODE.SINGLE;

	const queries: QueryWithValues[] = [];

	if (queryBatching === BATCH_MODE.SINGLE) {
		let columns: string[] = [];
		let insertItems: IDataObject[] = [];

		const priority = (nodeOptions.priority as string) || '';
		const ignore = (nodeOptions.skipOnConflict as boolean) ? 'IGNORE' : '';

		if (dataMode === DATA_MODE.AUTO_MAP) {
			columns = [
				...new Set(
					items.reduce((acc: string[], item: { json: {} }) => {
						const itemColumns = Object.keys(item.json);
						return acc.concat(itemColumns);
					}, [] as string[]),
				),
			];
			insertItems = this.helpers.copyInputItems(items, columns);
		}

		if (dataMode === DATA_MODE.MANUAL) {
			for (let i = 0; i < items.length; i++) {
				const fields = (this.getNodeParameter('fields', i, []) as IDataObject)
					.string as IDataObject[]; // Alterado de valuesToSend.values para fields.string
				const itemData = fields.reduce((acc: IDataObject, { name, value }) => {
					acc[name as string] = value;
					return acc;
				}, {});
				insertItems.push(itemData);
			}
		}

		columns = [
			...new Set(
				insertItems.reduce((acc: string[], item) => {
					const itemColumns = Object.keys(item);
					return acc.concat(itemColumns);
				}, [] as string[]),
			),
		];

		const insertQueries = insertItems.map((item) => {
			const columnsWithValues = columns.filter(
				(column) => item[column] !== null && item[column] !== undefined && item[column] !== '',
			);

			const processedValues = columnsWithValues.map((column) => {
				const value = item[column];
				// Obtém os metadados da coluna do Oracle
				const columnInfo = columnMetadata.find(
					(meta) =>
						typeof meta.value === 'string' && meta.value.toLowerCase() === column.toLowerCase(),
				);

				// Extrai o tamanho real e tipo da coluna do Oracle
				const metadata = columnInfo?.description
					? {
							maxLength: parseInt(columnInfo.description.match(/length: (\d+)/)?.[1] || '0', 10),
							dataType: columnInfo.description.match(/type: ([^,]+)/)?.[1],
						}
					: undefined;

				// Verifica valores especiais primeiro (SYSDATE, etc)
				if (typeof value === 'string') {
					const upperValue = value.trim().toUpperCase();
					if (
						upperValue === 'SYSDATE' ||
						upperValue.startsWith('(SELECT') ||
						upperValue.startsWith('SELECT') ||
						upperValue.startsWith('TO_DATE') ||
						upperValue.startsWith('TRUNC(')
					) {
						return value;
					}
				}

				// Processa o valor usando os metadados reais do Oracle
				const processed = specialValues(value, metadata);
				return processed.value;
			});

			const escapedColumns = columnsWithValues
				.map((column) => escapeSqlIdentifier(column))
				.join(', ');

			return {
				query: `INSERT ${priority} ${ignore} INTO ${escapeSqlIdentifier(table)} (${escapedColumns}) VALUES (${processedValues.join(', ')})`,
				values: [], // Sem binding parameters
			};
		});

		queries.push(...insertQueries);
	} else {
		// Existing batch mode logic remains unchanged
		for (let i = 0; i < items.length; i++) {
			let columns: string[] = [];
			let insertItem: IDataObject = {};

			const options = this.getNodeParameter('options', i);
			const priority = (options.priority as string) || '';
			const ignore = (options.skipOnConflict as boolean) ? 'IGNORE' : '';

			if (dataMode === DATA_MODE.AUTO_MAP) {
				columns = Object.keys(items[i].json);
				insertItem = columns.reduce((acc, key) => {
					if (columns.includes(key)) {
						acc[key] = items[i].json[key];
					}
					return acc;
				}, {} as IDataObject);
			}

			if (dataMode === DATA_MODE.MANUAL) {
				const valuesToSend = (this.getNodeParameter('valuesToSend', i, []) as IDataObject)
					.values as IDataObject[];

				insertItem = valuesToSend.reduce((acc, { name, value }) => {
					acc[name as string] = value;
					return acc;
				}, {} as IDataObject);

				columns = Object.keys(insertItem);
			}

			const escapedColumns = columns.map(escapeSqlIdentifier).join(', ');
			const valuesPlaceholders = columns.map((column) => `:${column}`).join(',');

			const query = `INSERT ${priority} ${ignore} INTO ${escapeSqlIdentifier(
				table,
			)} (${escapedColumns}) VALUES (${valuesPlaceholders})`;

			const values = columns.reduce((acc: { [key: string]: any }, column) => {
				const value = insertItem[column];
				acc[column] = {
					dir: oracledb.BIND_IN,
					type: value instanceof Date ? oracledb.DATE : oracledb.STRING,
					val: value !== null && value !== undefined ? value : null,
				};
				return acc;
			}, {}) as QueryValues;

			queries.push({ query, values });
		}
	}

	returnData = await runQueries(queries);

	return returnData;
}

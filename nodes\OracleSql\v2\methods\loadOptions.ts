import type { IDataObject, ILoadOptionsFunctions, INodePropertyOptions } from 'n8n-workflow';
import type { OracleSqlNodeCredentials } from '../helpers/interfaces';
import { createPool } from '../transport';
export async function getColumns(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
	const credentials = (await this.getCredentials('oracleSql')) as OracleSqlNodeCredentials;
	const nodeOptions = this.getNodeParameter('options', 0) as IDataObject;

	const pool = await createPool.call(this, credentials, nodeOptions);
	let connection;

	try {
		connection = await pool.getConnection();

		// Ensure table is a string and not an object
		const tableParam = this.getNodeParameter('table', 0, {
			extractValue: true,
		});

		// Convert to string, handling potential object input
		const table =
			typeof tableParam === 'object'
				? (tableParam as { value?: string }).value || ''
				: String(tableParam);

		//console.log('Processed Table Name:', table);

		const result = await connection.execute(
			`SELECT column_name, data_type, nullable, data_length
					FROM all_tab_columns
					WHERE UPPER(table_name) = UPPER(:tableName)
					AND UPPER(owner) = UPPER(:owner)`,
			{
				tableName: table,
				owner: credentials.user,
			},
		);

		//console.log('Raw Column Query Result:', result);

		if (!result.rows || result.rows.length === 0) {
			// Fallback to alternative query if first attempt fails
			const alternateResult = await connection.execute(
				`SELECT column_name, data_type, nullable, data_length
						FROM user_tab_columns
						WHERE UPPER(table_name) = UPPER(:tableName)`,
				{
					tableName: table,
				},
			);

			//console.log('Alternate Column Query Result:', alternateResult);

			if (!alternateResult.rows || alternateResult.rows.length === 0) {
				throw new Error(`No columns found for table: ${table}`);
			}

			return alternateResult.rows.map((column: unknown) => {
				const columnData = column as string[];
				return {
					name: columnData[0] as string,
					value: columnData[0] as string,
					description: `type: ${columnData[1]}, length: ${columnData[3]}, nullable: ${columnData[2]}`,
				};
			});
		}

		return result.rows.map((column: unknown) => {
			const columnData = column as string[];
			return {
				name: columnData[0] as string,
				value: columnData[0] as string,
				description: `type: ${columnData[1]}, length: ${columnData[3]}, nullable: ${columnData[2]}`,
			};
		});
	} catch (error) {
		console.error('Error fetching columns:', error);
		throw new Error(`Failed to fetch columns: ${(error as Error).message}`);
	} finally {
		if (connection) {
			try {
				await connection.close();
			} catch (error) {
				console.error('Error closing connection:', error);
			}
		}
	}
}

export async function getColumnsMultiOptions(
	this: ILoadOptionsFunctions,
): Promise<INodePropertyOptions[]> {
	const returnData = await getColumns.call(this);
	const returnAll = { name: '*', value: '*', description: 'All columns' };
	return [returnAll, ...returnData];
}

export async function getColumnsWithoutColumnToMatchOn(
	this: ILoadOptionsFunctions,
): Promise<INodePropertyOptions[]> {
	const columnToMatchOn = this.getNodeParameter('columnToMatchOn') as string;
	const returnData = await getColumns.call(this);
	return returnData.filter((column) => column.value !== columnToMatchOn);
}

import type {
	IDataObject,
	IExecuteFunctions,
	INodeExecutionData,
	INodeProperties,
} from 'n8n-workflow';

import type { QueryRunner, QueryValues, QueryWithValues } from '../../helpers/interfaces';
import { DATA_MODE, MANUAL } from '../../helpers/interfaces';

import { escapeSqlIdentifier, replaceEmptyStringsByNulls } from '../../helpers/utils';

import { updateDisplayOptions } from '../../../../../utils/utilities';
import { optionsCollection } from '../common.descriptions';

const properties: INodeProperties[] = [
	{
		displayName: 'Data Mode',
		name: 'dataMode',
		type: 'options',
		options: [
			{
				name: 'Auto-Map Input Data to Columns',
				value: DATA_MODE.AUTO_MAP,
				description: 'Use when node input properties names exactly match the table column names',
			},
			{
				name: 'Map Each Column Below',
				value: DATA_MODE.MANUAL,
				description: 'Set the value for each destination column manually',
			},
		],
		default: MANUAL,
		description:
			'Whether to map node input properties and the table data automatically or manually',
	},
	{
		displayName: `
		In this mode, make sure incoming data fields are named the same as the columns in your table. If needed, use an 'Edit Fields' node before this node to change the field names.
		`,
		name: 'notice',
		type: 'notice',
		default: '',
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.AUTO_MAP],
			},
		},
	},
	{
		// eslint-disable-next-line n8n-nodes-base/node-param-display-name-wrong-for-dynamic-options
		displayName: 'Column to Match On',
		name: 'columnToMatchOn',
		type: 'options',
		required: true,
		// eslint-disable-next-line n8n-nodes-base/node-param-description-wrong-for-dynamic-options
		description:
			'The column to compare when finding the rows to update. Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/" target="_blank">expression</a>.',
		typeOptions: {
			loadOptionsMethod: 'getColumns',
			loadOptionsDependsOn: ['schema.value', 'table.value'],
		},
		default: '',
		hint: "Used to find the correct row to update. Doesn't get changed. Has to be unique.",
	},
	{
		displayName: 'Value of Column to Match On',
		name: 'valueToMatchOn',
		type: 'string',
		default: '',
		description:
			'Rows with a value in the specified "Column to Match On" that corresponds to the value in this field will be updated. New rows will be created for non-matching items.',
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
	},
	{
		displayName: 'Values to Send',
		name: 'fields', // mudado de 'valuesToSend' para 'fields'
		placeholder: 'Add Field',
		type: 'fixedCollection',
		typeOptions: {
			multipleValueButtonText: 'Add Field',
			multipleValues: true,
		},
		displayOptions: {
			show: {
				dataMode: [DATA_MODE.MANUAL],
			},
		},
		default: {},
		options: [
			{
				displayName: 'Fields', // mudado de 'Values' para 'Fields'
				name: 'string', // mudado de 'values' para 'string'
				values: [
					{
						displayName: 'Field', // mudado de 'Column' para 'Field'
						name: 'name', // mudado de 'column' para 'name'
						type: 'options',
						typeOptions: {
							loadOptionsMethod: 'getColumnsWithoutColumnToMatchOn',
							loadOptionsDependsOn: ['schema.value', 'table.value'],
						},
						default: [],
					},
					{
						displayName: 'Value',
						name: 'value',
						type: 'string',
						default: '',
					},
				],
			},
		],
	},
	optionsCollection,
];

const displayOptions = {
	show: {
		resource: ['database'],
		operation: ['upsert'],
	},
	hide: {
		table: [''],
	},
};

export const description = updateDisplayOptions(displayOptions, properties);

export async function execute(
	this: IExecuteFunctions,
	inputItems: INodeExecutionData[],
	runQueries: QueryRunner,
	nodeOptions: IDataObject,
): Promise<INodeExecutionData[]> {
	let returnData: INodeExecutionData[] = [];
	const items = replaceEmptyStringsByNulls(inputItems, nodeOptions.replaceEmptyStrings as boolean);
	const queries: QueryWithValues[] = [];

	for (let i = 0; i < items.length; i++) {
		const table = this.getNodeParameter('table', i, undefined, {
			extractValue: true,
		}) as string;

		const columnToMatchOn = this.getNodeParameter('columnToMatchOn', i) as string;
		const dataMode = this.getNodeParameter('dataMode', i) as string;

		let item: IDataObject = {};
		const values: QueryValues = [];

		if (dataMode === DATA_MODE.AUTO_MAP) {
			item = items[i].json;
		}

		if (dataMode === DATA_MODE.MANUAL) {
			const fields = (this.getNodeParameter('fields', i, []) as IDataObject)
				.string as IDataObject[];

			item = fields.reduce((acc, { name, value }) => {
				acc[name as string] = value;
				return acc;
			}, {} as IDataObject);

			item[columnToMatchOn] = this.getNodeParameter('valueToMatchOn', i) as string;
		}

		const columns = Object.keys(item);
		const escapedColumns = columns.map(escapeSqlIdentifier).join(', ');
		const updateColumns = columns.filter((column) => column !== columnToMatchOn);

		// Processa os valores para inserção/atualização
		const processValue = (value: any): string => {
			if (value === null || value === undefined) {
				return 'NULL';
			} else if (Object.prototype.toString.call(value) === '[object Date]') {
				const dateValue = value as Date;
				const formattedDate = dateValue
					.toLocaleString('pt-BR', {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit',
						second: '2-digit',
						hour12: false,
					})
					.replace(',', '');
				return `TO_DATE('${formattedDate}', 'DD/MM/YYYY HH24:MI:SS')`;
			} else if (typeof value === 'string' && value.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
				return `TO_DATE('${value} 00:00:00', 'DD/MM/YYYY HH24:MI:SS')`;
			} else if (
				typeof value === 'string' &&
				(value.toUpperCase() === 'SYSDATE' || value.trim().toUpperCase().startsWith('(SELECT'))
			) {
				// Não adicionar aspas para SYSDATE ou comandos SELECT
				return value;
			} else if (typeof value === 'string') {
				return `'${value.replace(/'/g, "''")}'`;
			} else if (typeof value === 'boolean') {
				return value ? '1' : '0';
			}
			return String(value);
		};

		// Constrói os valores para INSERT e UPDATE
		const insertValues = columns.map((column) => processValue(item[column])).join(', ');
		const updateSetClause = updateColumns
			.map((column) => `${escapeSqlIdentifier(column)} = ${processValue(item[column])}`)
			.join(', ');

		// Divide em UPDATE + INSERT condicional sem usar alias
		const updateQuery = `
			UPDATE ${escapeSqlIdentifier(table)}
			   SET ${updateSetClause}
			 WHERE ${escapeSqlIdentifier(columnToMatchOn)} = ${processValue(item[columnToMatchOn])}`;

		const insertQuery = `
			INSERT INTO ${escapeSqlIdentifier(table)} (${escapedColumns})
			SELECT ${insertValues}
			  FROM dual
			 WHERE NOT EXISTS (
			SELECT 1 FROM ${escapeSqlIdentifier(table)}
		 	 WHERE ${escapeSqlIdentifier(columnToMatchOn)} = ${processValue(item[columnToMatchOn])}
			)`;

		queries.push({ query: updateQuery, values }, { query: insertQuery, values });
	}

	returnData = await runQueries(queries);

	return returnData;
}

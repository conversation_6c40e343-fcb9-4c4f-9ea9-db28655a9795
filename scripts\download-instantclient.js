const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Script para baixar e extrair o Oracle Instant Client automaticamente
 */

const INSTANT_CLIENT_URL = 'https://download.oracle.com/otn_software/nt/instantclient/1926000/instantclient-basic-windows.x64-*********.0dbru.zip';
const TARGET_DIR = path.join(__dirname, '..', 'nodes', 'OracleSql', 'v2', 'lib', 'instantclient');
const TEMP_ZIP = path.join(__dirname, '..', 'temp_instantclient.zip');

console.log('🔄 Iniciando download do Oracle Instant Client...');
console.log(`📁 Diretório de destino: ${TARGET_DIR}`);

// Criar diretório se não existir
if (!fs.existsSync(TARGET_DIR)) {
    fs.mkdirSync(TARGET_DIR, { recursive: true });
    console.log('📁 Diretório criado:', TARGET_DIR);
}

// Verificar se já existe
const ociPath = path.join(TARGET_DIR, 'oci.dll');
if (fs.existsSync(ociPath)) {
    console.log('✅ Oracle Instant Client já está instalado!');
    console.log('📍 Localização:', ociPath);
    process.exit(0);
}

console.log('⚠️ ATENÇÃO: Este script baixará o Oracle Instant Client da Oracle.');
console.log('📄 Você deve aceitar a licença da Oracle para usar este software.');
console.log('🔗 Licença: https://www.oracle.com/downloads/licenses/instant-client-lic.html');
console.log('');

// Função para baixar arquivo
function downloadFile(url, dest) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(dest);
        
        https.get(url, (response) => {
            if (response.statusCode === 302 || response.statusCode === 301) {
                // Seguir redirecionamento
                return downloadFile(response.headers.location, dest).then(resolve).catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;
            
            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                const percent = ((downloadedSize / totalSize) * 100).toFixed(1);
                process.stdout.write(`\r📥 Baixando: ${percent}% (${(downloadedSize / 1024 / 1024).toFixed(1)}MB / ${(totalSize / 1024 / 1024).toFixed(1)}MB)`);
            });
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log('\n✅ Download concluído!');
                resolve();
            });
            
            file.on('error', (err) => {
                fs.unlink(dest, () => {}); // Remover arquivo parcial
                reject(err);
            });
        }).on('error', reject);
    });
}

// Função para extrair ZIP
function extractZip(zipPath, extractTo) {
    try {
        console.log('📦 Extraindo arquivo ZIP...');
        
        // Usar PowerShell para extrair (disponível no Windows)
        const command = `powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${path.dirname(extractTo)}' -Force"`;
        execSync(command, { stdio: 'inherit' });
        
        // Mover arquivos da subpasta instantclient_xx_x para o diretório de destino
        const extractedDir = path.dirname(extractTo);
        const subDirs = fs.readdirSync(extractedDir).filter(item => 
            fs.statSync(path.join(extractedDir, item)).isDirectory() && 
            item.startsWith('instantclient')
        );
        
        if (subDirs.length > 0) {
            const sourceDir = path.join(extractedDir, subDirs[0]);
            const files = fs.readdirSync(sourceDir);
            
            console.log(`📁 Movendo arquivos de ${sourceDir} para ${extractTo}...`);
            
            for (const file of files) {
                const sourcePath = path.join(sourceDir, file);
                const destPath = path.join(extractTo, file);
                fs.renameSync(sourcePath, destPath);
            }
            
            // Remover diretório vazio
            fs.rmdirSync(sourceDir);
        }
        
        console.log('✅ Extração concluída!');
        
    } catch (error) {
        console.error('❌ Erro ao extrair:', error.message);
        throw error;
    }
}

// Processo principal
async function main() {
    try {
        // Baixar arquivo
        await downloadFile(INSTANT_CLIENT_URL, TEMP_ZIP);
        
        // Extrair arquivo
        extractZip(TEMP_ZIP, TARGET_DIR);
        
        // Remover arquivo temporário
        fs.unlinkSync(TEMP_ZIP);
        console.log('🧹 Arquivo temporário removido');
        
        // Verificar se foi instalado corretamente
        if (fs.existsSync(ociPath)) {
            console.log('✅ Oracle Instant Client instalado com sucesso!');
            console.log('📍 Localização:', ociPath);
            
            // Listar arquivos instalados
            const files = fs.readdirSync(TARGET_DIR);
            console.log('📋 Arquivos instalados:');
            files.forEach(file => console.log(`   - ${file}`));
            
        } else {
            console.error('❌ Falha na instalação: oci.dll não encontrado');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Erro durante a instalação:', error.message);
        console.log('');
        console.log('💡 Alternativas:');
        console.log('1. Baixe manualmente de: https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html');
        console.log(`2. Extraia para: ${TARGET_DIR}`);
        console.log('3. Certifique-se de que oci.dll está presente');
        process.exit(1);
    }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
    main();
}

module.exports = { downloadFile, extractZip, TARGET_DIR };

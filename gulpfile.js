const path = require('path');
const { task, src, dest } = require('gulp');

task('build:icons', copyIcons);

function copyIcons() {
	// Copia os ícones do Node
	const nodeSource = path.resolve('nodes', '**', '*.{png,svg}');
	const nodeDestination = path.resolve('dist', 'nodes');

	src(nodeSource).pipe(dest(nodeDestination));

	// Copia os ícones compartilhados para cada versão do OracleSql
	const iconSource = path.resolve('nodes/OracleSql/*.svg');

	// Copia para v2
	src(iconSource).pipe(dest('dist/nodes/OracleSql/v2'));

	// Copia para v1 (quando existir)
	src(iconSource).pipe(dest('dist/nodes/OracleSql/v1'));

	// Copia os ícones de credenciais
	const credSource = path.resolve('credentials', '**', '*.{png,svg}');
	const credDestination = path.resolve('dist', 'credentials');

	src(credSource).pipe(dest(credDestination));

	// Copia os ícones do Oracle para a pasta de credenciais
	// Isso garante que os ícones estejam disponíveis para o teste de credenciais
	return src(iconSource).pipe(dest(credDestination));
}

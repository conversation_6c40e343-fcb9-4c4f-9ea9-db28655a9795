# n8n-nodes-oraclesql-v2

Este é um nó comunitário para n8n que permite usar o Oracle SQL em seus fluxos de trabalho n8n.

[n8n](https://n8n.io/) é uma plataforma de automação de fluxo de trabalho com licença fair-code.

## Instalação

Siga o [guia de instalação](https://docs.n8n.io/integrations/community-nodes/installation/) na documentação de nós comunitários do n8n.

## Operações Suportadas

- **Executar Query**: Execute consultas SQL personalizadas
- **Selecionar**: Recupere dados de uma tabela
- **Inserir**: Insira novos registros em uma tabela
- **Atualizar**: Atualize registros existentes em uma tabela
- **Excluir**: Exclua registros de uma tabela
- **Trigger**: Monitore alterações em tabelas Oracle (INSERT, UPDATE, DELETE)

## Modos de Driver Oracle

A partir da versão 2.0, o nó OracleSQL suporta dois modos de driver:

### Modo Thin (Padrão)

O modo Thin é o padrão e não requer a instalação do Oracle Client. É uma implementação puramente JavaScript que se conecta diretamente ao banco de dados Oracle.

**Vantagens:**

- Não requer instalação do Oracle Client
- Ocupa menos espaço em disco (aproximadamente 1MB vs 240MB do modo Thick)
- Mais fácil de configurar e usar

**Limitações:**

- Suporta apenas Oracle Database 12.1 e superior
- Não suporta alguns recursos avançados disponíveis no modo Thick

### Modo Thick

O modo Thick usa as bibliotecas Oracle Client para se comunicar com o banco de dados Oracle.

**Vantagens:**

- Suporta Oracle Database 9.2 e superior (dependendo da versão do Oracle Client)
- Suporta recursos avançados como SODA, AQ, CQN, etc.
- Melhor desempenho em algumas operações

**Limitações:**

- Requer a instalação do Oracle Client
- Ocupa mais espaço em disco
- Mais complexo de configurar

## Configuração do Modo do Driver

Por padrão, o nó usa o modo Thin. Para usar o modo Thick, você tem duas opções:

### Opção 1: Configurar nas Credenciais (Recomendado)

1. Instalar o Oracle Client (Instant Client ou instalação completa)
2. Nas credenciais do Oracle SQL, selecione "Thick" no campo "Driver Mode"
3. Salve as credenciais e use-as em seus nós

Esta opção permite alternar entre os modos Thin e Thick sem reiniciar o n8n.

### Opção 2: Configurar via Variável de Ambiente

1. Instalar o Oracle Client (Instant Client ou instalação completa)
2. Configurar a variável de ambiente `NODE_ORACLEDB_DRIVER_MODE=thick`
3. Reiniciar o n8n

O nó detectará automaticamente bancos de dados antigos (versões anteriores a 12c) e recomendará o uso do modo Thick quando necessário.

## Detecção Automática de Bancos Antigos

O nó inclui uma funcionalidade que detecta automaticamente bancos de dados antigos (versões anteriores a 12c) e exibe um aviso recomendando o uso do modo Thick. Isso ajuda a evitar problemas de compatibilidade.

## Compatibilidade

- Versão mínima do n8n: 0.214.0
- Testado com Oracle Database 11g, 12c, 19c e 21c

## Recursos

- [Documentação de nós comunitários do n8n](https://docs.n8n.io/integrations/community-nodes/)
- [Documentação do Oracle Database](https://docs.oracle.com/en/database/)
- [Documentação do node-oracledb](https://node-oracledb.readthedocs.io/)

## Histórico de Versões

### 2.1.0

- Adicionada opção de seleção de modo do driver (Thin/Thick) nas credenciais
- Implementada detecção e aplicação dinâmica de mudanças no modo do driver sem necessidade de reiniciar o n8n
- Melhoradas as mensagens de erro e avisos relacionados ao modo do driver

### 2.0.0

- Adicionado suporte ao modo Thin por padrão
- Implementada detecção automática de bancos antigos
- Melhorada a mensagem de teste de credenciais para mostrar o modo do driver
- Adicionado fallback para o modo Thick quando necessário

### 1.0.0

- Versão inicial com suporte apenas ao modo Thick

## Licença

[MIT](LICENSE.md)

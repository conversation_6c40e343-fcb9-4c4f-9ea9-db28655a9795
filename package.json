{"name": "n8n-nodes-oraclesql-v2", "version": "2.0.0", "description": "Node OracleSql by TECH NERVS", "keywords": ["n8n-community-node-package"], "license": "MIT", "homepage": "https://technervs.com/", "author": {"name": "TECH NERVS", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/technervs/n8n-nodes-oraclesql.git"}, "main": "index.js", "scripts": {"build": "rd /s /q dist && tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint nodes credentials package.json", "lintfix": "eslint nodes credentials package.json --fix", "prepublishOnly": "npm run build && npm run lint -c .eslintrc.prepublish.js nodes credentials package.json"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/OracleSql.credentials.js"], "nodes": ["dist/nodes/OracleSql/OracleSql.node.js", "dist/nodes/OracleSql/OracleSqlTrigger.node.js"]}, "devDependencies": {"@types/lodash": "^4.17.17", "@types/oracledb": "^6.6.0", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-plugin-n8n-nodes-base": "^1.16.3", "gulp": "^5.0.0", "n8n-workflow": "^1.82.0", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "peerDependencies": {"oracledb": "^6.8.0"}}